#!/bin/bash

echo "🚀 设置子域名分离安全方案"
echo "================================="

# 1. 检查当前目录
if [ ! -f "config/tenancy.php" ]; then
    echo "❌ 请在Laravel项目根目录运行此脚本"
    exit 1
fi

# 2. 备份当前配置
echo "📝 备份当前配置..."
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)

# 3. 更新环境配置
echo "🔧 更新.env配置..."
sed -i '' 's|APP_URL="https://scoredb25.ddev.site"|APP_URL="https://hiddenadmin.scoredb25.ddev.site"|g' .env
echo 'CENTRAL_ADMIN_DOMAIN="hiddenadmin.scoredb25.ddev.site"' >> .env

# 4. 重启DDEV以应用新的域名配置
echo "🔄 重启DDEV..."
ddev restart

# 5. 清除现有租户数据（如果存在）
echo "🗑️  清除现有租户数据..."
php artisan migrate:fresh --seed

echo ""
echo "✅ 子域名分离配置完成！"
echo ""
echo "🔗 访问地址："
echo "   管理员登录: https://hiddenadmin.scoredb25.ddev.site/login"
echo "   默认租户:   https://default.scoredb25.ddev.site/login"
echo "   测试租户:   https://test.scoredb25.ddev.site/login"
echo ""
echo "🔐 安全提升："
echo "   ✅ 管理员域名隐藏"
echo "   ✅ 租户域名隔离"
echo "   ✅ 无法通过猜测发现其他租户" 