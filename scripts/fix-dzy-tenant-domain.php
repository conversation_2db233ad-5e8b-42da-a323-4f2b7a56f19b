<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->boot();

echo "🔧 为 <EMAIL> 租户添加域名\n";
echo "=====================================\n\n";

// 查找租户
$tenant = App\Models\Tenant::where('email', '<EMAIL>')->first();

if (!$tenant) {
    echo "❌ 未找到邮箱为 <EMAIL> 的租户\n";
    exit(1);
}

echo "✅ 找到租户: {$tenant->id}\n";
echo "   邮箱: {$tenant->email}\n";

// 检查是否已有域名
$existingDomains = $tenant->domains;
if ($existingDomains->count() > 0) {
    echo "⚠️  租户已有域名:\n";
    foreach ($existingDomains as $domain) {
        echo "   - {$domain->domain} (primary: " . ($domain->is_primary ? 'yes' : 'no') . ")\n";
    }
} else {
    echo "📝 租户当前无域名，正在创建...\n";
    
    // 创建子域名
    $domain = $tenant->createDomain('dzy.scoredb25.ddev.site');
    $domain->makePrimary();
    
    echo "✅ 创建域名成功: {$domain->domain}\n";
    echo "   Primary: " . ($domain->is_primary ? 'yes' : 'no') . "\n";
}

// 测试路由生成
try {
    $loginRoute = $tenant->route('tenant.login');
    echo "\n✅ 路由生成测试成功: {$loginRoute}\n";
} catch (Exception $e) {
    echo "\n❌ 路由生成失败: {$e->getMessage()}\n";
}

echo "\n🎉 修复完成！\n";
echo "\n📍 您的访问地址:\n";
echo "   租户登录: https://dzy.scoredb25.ddev.site/login\n";
echo "   邮箱: <EMAIL>\n";
echo "   密码: password (默认密码)\n";

echo "\n💡 现在您可以:\n";
echo "1. 访问中央登录: https://scoredb25.ddev.site/login\n";
echo "2. 输入邮箱: <EMAIL>\n";
echo "3. 系统会重定向到: https://dzy.scoredb25.ddev.site/login\n";
echo "4. 在租户登录页面输入邮箱和密码完成登录\n"; 