#!/bin/bash

echo "🔧 修复租户Primary域名问题"
echo "================================"

# 1. 检查当前目录
if [ ! -f "config/tenancy.php" ]; then
    echo "❌ 请在Laravel项目根目录运行此脚本"
    exit 1
fi

# 2. 备份当前环境文件
echo "📝 备份.env文件..."
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)

# 3. 更新环境变量
echo "🔧 更新环境变量..."
if grep -q "CENTRAL_ADMIN_DOMAIN" .env; then
    echo "   CENTRAL_ADMIN_DOMAIN 已存在"
else
    echo 'CENTRAL_ADMIN_DOMAIN="hiddenadmin.scoredb25.ddev.site"' >> .env
    echo "   ✅ 添加了 CENTRAL_ADMIN_DOMAIN"
fi

# 更新APP_URL
sed -i.bak 's|APP_URL="https://scoredb25.ddev.site"|APP_URL="https://hiddenadmin.scoredb25.ddev.site"|g' .env
echo "   ✅ 更新了 APP_URL"

# 4. 重启DDEV
echo "🔄 重启DDEV以应用新配置..."
ddev restart

# 5. 清理并重新创建数据库
echo "🗑️  清理现有数据..."
ddev exec php artisan migrate:fresh

# 6. 创建正确的租户数据
echo "🏗️  创建子域名格式的租户..."
ddev exec php artisan db:seed --class=TenantSeeder

echo ""
echo "✅ 修复完成！"
echo ""
echo "🔗 新的访问地址："
echo "   管理员登录: https://hiddenadmin.scoredb25.ddev.site/login"
echo "   默认租户:   https://default.scoredb25.ddev.site/login"
echo "   测试租户:   https://test.scoredb25.ddev.site/login"
echo ""
echo "🧪 测试登录："
echo "   在管理员登录页面输入: <EMAIL>"
echo "   系统会重定向到: https://default.scoredb25.ddev.site/login" 