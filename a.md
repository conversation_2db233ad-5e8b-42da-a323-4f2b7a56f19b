scoreDB25/saas是一个laravel saas模板系统(基于https://tenancyforlaravel.com/saas-boilerplate/ 内部未公开发布的v4版本)，并且已经开发了saas相关的基础框架, 这个框架会定期更新。这个saas框架模板系统的说明在saas-docs1和saas-docs2目录中。saas框架使用了 Laravel Cashier 的标准表结构（subscriptions 和 subscription_items 表），以及 Tenant 模型中的 Billable trait。不使用sanctum和passport。

我们要基于这个saas框架开发一个教学事务管理系统(laravel 12 + shadcn-vue + postgresql + saas)，使用多租户+多数据库的架构。我们将开发2个大的功能，一是成绩分析系统，二是工资计算和查询系统，它们放在一个系统中，系统可以根据用户的订阅，将成绩分析和工资计算和查询功能分开授权给租户。并且我们将功能分成三个大模块core, score, salary。我们将优先开发成绩分析系统，工资计算和查询系统暂不开发。
我们使用macOS系统，ddev环境开发，以下是ddev首次设置的命令
cd /Users/<USER>/Code/scoreDB25/saas && ddev config --project-type=laravel --docroot=public --php-version=8.4 --project-name=scoredb25 --database=postgres:17

docs目录是新系统开发的相关文档，docs目录中的文档，要确保它们全面、明确、细致，文档大小适中，并且与saas-docs1和saas-docs2目录中的文档不冲突。saas_boilerplate目录是框架，它会定期更新，但我们永远不会手工修改它。saas是新系统的代码,它最开始是复制了saas_boilerplate目录中的代码，然后我们根据需求进行了修改，这是我们进行开发的主要目录。将来saas_boilerplate这个框架会更新，而saas的很多文件要基于saas_boilerplate的更新而经常更新。我们要尽力避免对框架文件进行修改太多，以便将来也能够利用saas_boilerplate的新版本框架进行快速升级。

我们已经写了seed，准备了大量的demo数据。我们现在的重点是规划域名、路由和以下逻辑：
1、主域名URL: https://scoredb25.ddev.site 或 www.scoredb25.ddev.site
显示一些宣传页面，用户注册后，可以购买订阅，设置子域名，如果子域名已存在或包括禁用字符，则提示用户重新设置，子域名一旦确定，就禁止修改，并且自动进行租户的设置，并且订阅的账号自动成为子域名租户的管理员。如果用户没有订阅，则无法设置子域名。

假设某个用户设置了子域名为school1，则该租户的所有用户的域名地址为 school1.scoredb25.ddev.site, 租户管理员可以在2个地方登录，一个是https://school1.scoredb25.ddev.site/login，一个是https://scoredb25.ddev.site/login#/school1，在主域名登录后，不会自动跳转到子域名，因为在主域名可以进行订阅或续费等任务。在主域名登录后，可以自动登录到 https://school1.scoredb25.ddev.site/login,但不能反过来。

2、系统管理后台
URL: https://hiddenadmin.scoredb25.ddev.site/admin/login
邮箱: 您用命令创建的邮箱
密码: 您设置的密码
系统管理后台，通常只有1个或少量用户，不提供给订阅用户访问, 默认也不提供注册功能。提供对订阅用户的管理、订阅方案的修订、某些选项的开放等各种功能。

3、默认租户（学校1）
URL: https://default.scoredb25.ddev.site/login
邮箱: <EMAIL>
密码: password
我不知道有没有必要设置默认租户，先这样设置吧，以后再看。

4、测试租户（学校2）
URL: https://test.scoredb25.ddev.site/login
邮箱: <EMAIL>
密码: password
测试租户有大量模拟数据。

主域名可能是变化的，例如将来我可能使用 goodsaleabcd.com这样的主域名。那么系统管理后台域名就是 hiddenadmin.goodsaleabcd.com，各租户的域名就是 school1.goodsaleabcd.com, school2.goodsaleabcd.com, etc.

各租户可能提供给一个年级，也可能提供给一个学校(包括多个年级)，每个年级有多个班级，每个租户中，涉及以下角色:教师、班主任、年级主任、学生、家长、工作人员。还有校长、教务主任、德育主任等部门主任的角色。涉及多个部门，教务处、德育处、总务处、办公室等。

系统可以限定每个租户的年级数量和学生、教师数量。
考试的成绩涉及不同类型，例如月考、期中、期末、单元测试、模拟考试等。
考试学科涉及语文、数学、英语、物理、化学、生物、政治、历史、地理等。
对成绩的分析统计涉及多种维度，例如班级、年级、学科、考试类型等。还可以以学生个人角度进行分析，例如学生的成绩趋势、学科强弱项等。可以对老师进行分析，例如教学质量评估、学生满意度等。

