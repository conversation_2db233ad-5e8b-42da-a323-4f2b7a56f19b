# 用户角色和权限体系

## 概述
教学事务管理系统基于 scoreDB25/saas 框架的 RBAC（基于角色的访问控制）权限管理系统，结合教学场景的特殊需求，设计了完整的用户角色和权限体系。系统支持多层级的角色管理，确保不同用户只能访问其职责范围内的功能和数据。

## 角色层次结构

### 系统级角色（中央管理）
- **系统管理员（Super Admin）**
  - 访问域名：`hiddenadmin.scoredb25.ddev.site`
  - 权限范围：整个系统的管理和配置
  - 主要职责：租户管理、订阅管理、系统配置

### 租户级角色（学校内部）

#### 管理层角色
1. **校长（Principal）**
   - 权限范围：全校数据查看和重要决策
   - 主要功能：全校数据概览、重要报表审批、系统配置

2. **教务主任（Academic Director）**
   - 权限范围：教学相关的全部数据管理
   - 主要功能：成绩管理、考试安排、教学质量分析

3. **德育主任（Student Affairs Director）**
   - 权限范围：学生管理和德育相关数据
   - 主要功能：学生信息管理、班级管理、德育评估

#### 教学角色
4. **年级主任（Grade Director）**
   - 权限范围：所负责年级的全部数据
   - 主要功能：年级成绩分析、年级教师管理、年级统计

5. **班主任（Class Teacher）**
   - 权限范围：所负责班级的全部数据
   - 主要功能：班级成绩管理、学生管理、家长沟通

6. **学科组长（Subject Leader）**
   - 权限范围：所负责学科的全校数据
   - 主要功能：学科成绩分析、学科教学质量评估

7. **任课教师（Subject Teacher）**
   - 权限范围：所教授班级和学科的数据
   - 主要功能：成绩录入、学科分析、个人薪资查询

#### 行政角色
8. **工作人员（Staff）**
   - 权限范围：基础数据录入和查询
   - 主要功能：数据录入、基础查询、个人薪资查询

#### 用户角色
9. **学生（Student）**
   - 权限范围：个人学习数据查看
   - 主要功能：个人成绩查询、学习分析、排名查看

10. **家长（Parent）**
    - 权限范围：子女学习数据查看
    - 主要功能：子女成绩查询、学习报告、与教师沟通

## 权限矩阵

### Core 模块权限
| 角色 | 用户管理 | 班级管理 | 年级管理 | 学科管理 | 部门管理 | 系统配置 |
|------|----------|----------|----------|----------|----------|----------|
| 校长 | 查看 | 查看 | 查看 | 查看 | 查看 | 部分 |
| 教务主任 | 管理 | 管理 | 管理 | 管理 | 查看 | 无 |
| 德育主任 | 部分管理 | 管理 | 查看 | 查看 | 查看 | 无 |
| 年级主任 | 查看本年级 | 查看本年级 | 管理本年级 | 查看 | 查看 | 无 |
| 班主任 | 查看本班 | 管理本班 | 查看本年级 | 查看 | 查看 | 无 |
| 学科组长 | 查看相关 | 查看 | 查看 | 管理本学科 | 查看 | 无 |
| 任课教师 | 查看相关 | 查看相关 | 查看相关 | 查看相关 | 查看 | 无 |
| 工作人员 | 无 | 查看 | 查看 | 查看 | 查看 | 无 |
| 学生 | 查看个人 | 查看本班 | 查看本年级 | 查看 | 无 | 无 |
| 家长 | 查看子女 | 查看相关 | 查看相关 | 查看 | 无 | 无 |

### Score 模块权限
| 角色 | 考试管理 | 成绩录入 | 成绩查询 | 成绩分析 | 报表生成 | 数据导出 |
|------|----------|----------|----------|----------|----------|----------|
| 校长 | 查看 | 无 | 全校 | 全校 | 全校 | 全校 |
| 教务主任 | 管理 | 管理 | 全校 | 全校 | 全校 | 全校 |
| 德育主任 | 查看 | 无 | 全校 | 全校 | 部分 | 部分 |
| 年级主任 | 查看 | 审核本年级 | 本年级 | 本年级 | 本年级 | 本年级 |
| 班主任 | 查看 | 审核本班 | 本班 | 本班 | 本班 | 本班 |
| 学科组长 | 查看 | 本学科 | 本学科全校 | 本学科全校 | 本学科 | 本学科 |
| 任课教师 | 查看 | 所教班级学科 | 所教范围 | 所教范围 | 所教范围 | 所教范围 |
| 工作人员 | 无 | 协助录入 | 无 | 无 | 无 | 无 |
| 学生 | 查看相关 | 无 | 个人 | 个人 | 个人 | 个人 |
| 家长 | 查看相关 | 无 | 子女 | 子女 | 子女 | 子女 |

### Salary 模块权限
| 角色 | 工资配置 | 工资计算 | 工资查询 | 工资统计 | 报表生成 | 数据导出 |
|------|----------|----------|----------|----------|----------|----------|
| 校长 | 审批 | 审批 | 全校 | 全校 | 全校 | 全校 |
| 教务主任 | 无 | 无 | 无 | 无 | 无 | 无 |
| 德育主任 | 无 | 无 | 无 | 无 | 无 | 无 |
| 年级主任 | 无 | 无 | 个人 | 无 | 无 | 个人 |
| 班主任 | 无 | 无 | 个人 | 无 | 无 | 个人 |
| 学科组长 | 无 | 无 | 个人 | 无 | 无 | 个人 |
| 任课教师 | 无 | 无 | 个人 | 无 | 无 | 个人 |
| 工作人员 | 管理 | 管理 | 全校 | 全校 | 全校 | 全校 |
| 学生 | 无 | 无 | 无 | 无 | 无 | 无 |
| 家长 | 无 | 无 | 无 | 无 | 无 | 无 |

## 数据访问范围

### 按角色定义的数据范围
1. **全校范围**：校长、教务主任、德育主任、工作人员（薪资）
2. **年级范围**：年级主任（所负责年级）
3. **班级范围**：班主任（所负责班级）
4. **学科范围**：学科组长（所负责学科的全校数据）
5. **教学范围**：任课教师（所教授的班级和学科）
6. **个人范围**：学生（个人数据）、家长（子女数据）、教师（个人薪资）

### 数据隔离机制
- **租户级隔离**：不同学校的数据完全隔离
- **角色级隔离**：基于角色的数据访问控制
- **关系级隔离**：基于用户与数据的关系进行访问控制

## 权限实现机制

### 基于框架的权限系统
系统基于 scoreDB25/saas 框架的权限系统，扩展教学场景的特殊需求：

```php
// 权限检查示例
class ScoreController extends Controller
{
    public function index(Request $request)
    {
        // 检查模块访问权限
        $this->authorize('access-module', 'score');
        
        // 根据角色获取数据范围
        $scores = $this->getScoresByUserRole(auth()->user());
        
        return response()->json($scores);
    }
    
    private function getScoresByUserRole(User $user)
    {
        if ($user->hasRole('principal') || $user->hasRole('academic_director')) {
            // 全校数据
            return Score::all();
        } elseif ($user->hasRole('grade_director')) {
            // 年级数据
            return Score::whereIn('class_id', $user->managedGrades->pluck('classes')->flatten()->pluck('id'));
        } elseif ($user->hasRole('class_teacher')) {
            // 班级数据
            return Score::whereIn('class_id', $user->managedClasses->pluck('id'));
        }
        // ... 其他角色逻辑
    }
}
```

### 中间件权限控制
```php
// 模块访问中间件
Route::middleware(['ensure.module.access:score', 'role:teacher|admin'])
    ->group(function () {
        Route::get('/scores', 'ScoreController@index');
    });
```

## 角色分配和管理

### 角色分配原则
1. **最小权限原则**：用户只获得完成工作所需的最小权限
2. **职责分离原则**：重要操作需要多个角色协作完成
3. **定期审核原则**：定期审核用户权限，及时调整

### 角色管理流程
1. **角色创建**：由校长或教务主任创建新用户并分配角色
2. **权限变更**：角色变更需要上级管理员审批
3. **权限审核**：定期审核用户权限，确保权限合理性

## 特殊权限控制

### 敏感操作权限
- **成绩修改**：需要多级审核（录入→审核→确认）
- **用户删除**：只有校长和教务主任可以删除用户
- **系统配置**：只有校长可以修改重要系统配置
- **数据导出**：根据角色限制导出数据的范围和格式

### 时间限制权限
- **成绩录入期限**：超过期限后需要特殊权限才能修改
- **考试安排权限**：考试开始后不能修改考试信息
- **报表生成权限**：某些报表只能在特定时间段生成

## 安全措施

### 权限验证
- **双重验证**：重要操作需要密码确认
- **操作日志**：记录所有敏感操作的详细日志
- **异常监控**：监控异常的权限使用行为

### 数据保护
- **敏感数据加密**：学生个人信息和成绩数据加密存储
- **访问审计**：记录所有数据访问行为
- **数据备份**：定期备份重要数据，确保数据安全
