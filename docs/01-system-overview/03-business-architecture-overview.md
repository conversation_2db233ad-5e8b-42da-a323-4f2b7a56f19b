# 业务架构概览

> **文档职责**: 本文档专注于 scoreDB25 教学事务管理系统的业务架构设计、模块关系和数据流程。
> 如需了解技术栈选型，请参考 [技术栈架构](02-technical-stack-architecture.md)；
> 如需了解具体开发实现，请参考 [开发文档/系统架构文档](../04-development-docs/09-system-architecture-document.md)。

## 1. 概述

EduSaaS 是一个基于 Laravel 12 + PostgreSQL + shadcn-vue 开发的多租户 SaaS 教学事务管理系统，实现了成绩分析和工资管理两个核心模块的混合与集成。本文档介绍系统的全局架构设计，为开发者和系统管理员提供高层次的系统概览。

## 2. 宏观架构

### 2.1 系统总体架构

```mermaid
flowchart TB
    subgraph "中央平台"
        Central["中央管理控制台"] --> TenantMgmt["租户管理"]
        Central --> SubMgmt["订阅管理"]
        SubMgmt --> Stripe["Stripe集成"]
    end

    subgraph "租户空间"
        Auth["认证系统"]
        RBAC["角色权限管理"]
        
        subgraph "核心模块"
            UserMgmt["用户管理"]
            Settings["系统设置"]
        end
        
        subgraph "扩展模块"
            ScoreModule["成绩分析系统"]
            SalaryModule["工资管理系统"]
        end
        
        Auth --> RBAC
        RBAC --> UserMgmt
        RBAC --> Settings
        RBAC --> ScoreModule
        RBAC --> SalaryModule
    end
    
    subgraph "中间件层"
        TenantResolver["租户识别中间件"]
        ModuleAccess["EnsureModuleAccess中间件"]
    end
    
    subgraph "数据存储"
        CentralDB[("中央数据库")]
        TenantDB[("租户数据库")]
    end
    
    CentralDB --> Central
    TenantDB --> Auth
    TenantDB --> ScoreModule
    TenantDB --> SalaryModule
    
    TenantMgmt --> TenantResolver
    SubMgmt --> ModuleAccess
    ModuleAccess --> ScoreModule
    ModuleAccess --> SalaryModule
    
    TenantResolver --> Auth

    subgraph "集成层"
        Integration["模块集成服务"]
    end

    ScoreModule <--> Integration
    SalaryModule <--> Integration
```

### 2.2 多租户数据分离架构

```mermaid
graph TD
    CentralDB[("中央数据库")] --> Tenant1[("租户 1 数据库")]
    CentralDB --> Tenant2[("租户 2 数据库")]
    CentralDB --> Tenant3[("租户 3 数据库")]
    
    subgraph 中央管理平台
        CentralDB
    end
    
    subgraph 租户数据
        Tenant1
        Tenant2
        Tenant3
    end
```

### 2.3 订阅与模块控制流程

```mermaid
flowchart LR
    A[请求] --> B[认证状态检查]
    B -->|Unauthorized| F[401错误]
    B -->|Authorized| C[租户检查]
    C -->|Invalid Tenant| G[404错误]
    C -->|Valid Tenant| D[EnsureModuleAccess中间件]
    D -->|No Access| E[Subscription Required页面]
    D -->|Has Access| H[模块控制器处理请求]
```

## 3. 响应式经典三层架构

```mermaid
graph TD
    subgraph "表现层"
        Controllers["控制器"]
        Resources["资源转换"]    
        Views["前端视图"] 
    end
    
    subgraph "业务层"
        Services["服务层"]
        Integration["集成服务"]
        Events["事件系统"]
    end
    
    subgraph "数据层"
        Repositories["仓库层"]
        Models["模型层"]
    end
    
    Controllers --> Services
    Resources --> Services
    Views --> Controllers
    
    Services --> Repositories
    Services --> Events
    Services <--> Integration
    
    Repositories --> Models
```

## 4. 成绩分析模块架构

```mermaid
graph TD
    subgraph "成绩输入管理"
        ScoreEntry["成绩录入"] --> ScoreImport["批量导入"]
        ScoreEntry --> ScoreValidation["数据验证"]  
        ScoreImport --> ScoreApproval["审核流程"]
    end
    
    subgraph "成绩分析引擎"
        ScoreAnalytics["统计分析"] --> RankCalculation["排名计算"]
        ScoreAnalytics --> TrendAnalysis["趋势分析"]
        ScoreAnalytics --> DistributionAnalysis["分布分析"]
    end
    
    subgraph "成绩报表生成"
        Reports["报表引擎"] --> StudentReport["学生报告"]
        Reports --> ClassReport["班级报告"]
        Reports --> GradeReport["年级报告"] 
    end
    
    subgraph "可视化展示"
        Charts["图表生成"] --> Dashboards["仪表盘"] 
    end
    
    ScoreEntry --> ScoreAnalytics
    ScoreAnalytics --> Reports
    ScoreAnalytics --> Charts
    Reports --> Charts
```

## 5. 工资管理模块架构

```mermaid
graph TD
    subgraph "员工管理"
        EmployeeInfo["员工信息"] --> PositionMgmt["职位管理"]
        EmployeeInfo --> DepartmentMgmt["部门管理"]
    end
    
    subgraph "工资结构配置"
        SalaryStructure["工资组成"] --> ComponentMgmt["组成项管理"]
        SalaryStructure --> FormulaConfig["公式配置"]
    end
    
    subgraph "工资计算引擎"
        Calculator["计算引擎"] --> BasicCalculation["基本计算"]
        Calculator --> BonusCalculation["绩效计算"]
        Calculator --> DeductionCalculation["扣除计算"]
    end
    
    subgraph "工资单管理"
        Payslips["工资单生成"] --> ApprovalWorkflow["审批流程"]
        Payslips --> Distribution["工资发放"] 
    end
    
    subgraph "报表与分析"
        SalaryReports["报表生成"] --> SalaryExport["数据导出"]
        SalaryReports --> SalaryCharts["工资统计图表"]
    end
    
    EmployeeInfo --> SalaryStructure
    SalaryStructure --> Calculator
    EmployeeInfo --> Calculator
    Calculator --> Payslips
    Payslips --> SalaryReports
```

## 6. 模块集成架构

```mermaid
graph TD
    subgraph "成绩分析系统"
        ScoreAnalytics["成绩分析服务"] --> PerformanceMetrics["教学绩效指标"]
    end
    
    subgraph "工资管理系统"
        SalaryCalculator["工资计算服务"] --> TeacherPerformance["教师绩效计算"]
    end
    
    subgraph "集成服务"
        IntegrationService["模块集成服务"]
    end
    
    ScoreAnalytics <--> IntegrationService
    SalaryCalculator <--> IntegrationService
    PerformanceMetrics --> TeacherPerformance
```

## 7. 前端架构

```mermaid
graph TD
    subgraph "组件层次"
        Vue["基础 Vue 3 框架"] --> RadixVue["原子组件 (Radix Vue)"]
        RadixVue --> ShadcnVue["UI 组件库 (shadcn-vue)"]
        ShadcnVue --> AppComponents["应用组件"]
        AppComponents --> BusinessComponents["业务组件"]
    end
    
    subgraph "状态管理"
        Pinia["状态存储"] --> Stores["功能状态模块"]
        Stores --> SharedState["共享状态"]
    end
    
    subgraph "路由管理"
        VueRouter["路由系统"] --> Guards["路由守卫"]
        Guards --> ModuleRoutes["模块化路由"]
    end
    
    subgraph "API 集成"
        Axios["HTTP 客户端"] --> ApiServices["API 服务层"]
        ApiServices --> ApiInterceptors["拦截器"]
    end
    
    BusinessComponents --> Pinia
    BusinessComponents --> VueRouter
    BusinessComponents --> Axios
```

## 8. 部署架构

```mermaid
graph TD
    Internet["互联网"] --> CDN["CDN / Cloudflare"]
    CDN --> LB["负载均衡器"]
    
    subgraph "应用服务器集群"
        LB --> AppServer1["应用服务器 1"]
        LB --> AppServer2["应用服务器 2"]
        LB --> AppServerN["应用服务器 N"]
    end
    
    subgraph "数据存储"
        AppServer1 --> DB[("PostgreSQL 主库")]
        AppServer2 --> DB
        AppServerN --> DB
        
        DB --> DBReplica1[("PostgreSQL 只读副本 1")]
        DB --> DBReplica2[("PostgreSQL 只读副本 2")]
        
        AppServer1 --> Redis[("Redis 缓存")]
        AppServer2 --> Redis
        AppServerN --> Redis
        
        AppServer1 --> S3[("S3 对象存储")]
        AppServer2 --> S3
        AppServerN --> S3
    end
    
    subgraph "后台处理"
        AppServer1 --> Queue[("Redis 队列")]
        AppServer2 --> Queue
        AppServerN --> Queue
        
        Queue --> Worker1["工作进程 1"]
        Queue --> Worker2["工作进程 2"]
        Queue --> WorkerN["工作进程 N"]
        
        Worker1 --> DB
        Worker2 --> DB
        WorkerN --> DB
    end
    
    subgraph "监控与日志"
        Monitor["系统监控"]
        Logger["日志收集"]
        
        AppServer1 --> Monitor
        AppServer1 --> Logger
        Worker1 --> Logger
    end
```

## 9. 安全架构

```mermaid
graph TD
    subgraph "认证层"
        Auth["认证系统"] --> Session["Session认证"]
        Auth --> SocialLogin["社交登录 (Socialite)"]
    end
    
    subgraph "授权层"
        RBAC["基于角色的权限控制"] --> Roles["角色管理"]
        RBAC --> Permissions["权限管理"]
        RBAC --> Policies["策略定义"]
    end
    
    subgraph "数据安全"
        Encryption["数据加密"] --> Masking["敏感数据脱敏"]
        Encryption --> Storage["存储加密"]
    end
    
    subgraph "外部安全"
        Firewall["防火墙"] --> WAF["Web 应用防火墙"]
        Firewall --> RateLimiting["速率限制"]
        Firewall --> HTTPS["SSL/TLS 加密"]
    end
    
    subgraph "审计与监控"
        Audit["审计日志"] --> AlertSystem["危险警报"]
        Audit --> Monitoring["安全监控"]
    end
    
    Auth --> RBAC
    RBAC --> Encryption
    Firewall --> Auth
    RBAC --> Audit
```

## 10. 数据流图

### 10.1 成绩分析主流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 控制器
    participant S as 成绩服务
    participant R as 仓库层
    participant DB as 数据库
    
    U->>+C: 请求成绩分析
    C->>+S: 调用分析服务
    S->>+R: 查询成绩数据
    R->>+DB: 数据库查询
    DB-->>-R: 返回原始数据
    R-->>-S: 返回数据集合
    S->>S: 执行分析算法
    S-->>-C: 返回分析结果
    C-->>-U: 展示分析结果
```

### 10.2 工资计算主流程

```mermaid
sequenceDiagram
    participant U as 管理员
    participant C as 控制器
    participant S as 工资服务
    participant I as 集成服务
    participant PS as 成绩服务
    participant DB as 数据库
    
    U->>+C: 发起工资计算
    C->>+S: 调用计算服务
    S->>S: 基本计算
    S->>+I: 请求教师绩效数据
    I->>+PS: 查询成绩分析
    PS->>DB: 数据库查询
    DB-->>PS: 返回成绩数据
    PS-->>-I: 返回绩效指标
    I-->>-S: 返回绩效数据
    S->>S: 计算绩效工资
    S->>DB: 保存工资计算结果
    S-->>-C: 返回计算结果
    C-->>-U: 展示计算结果
```

## 11. 平台基础设施

```mermaid
graph TD
    subgraph "应用基础设施"
        Laravel["框架核心"] --> TenancyPkg["多租户框架"]
        Laravel --> CashierPkg["订阅支付框架"]
        Laravel --> DBManager["数据库管理"]
    end
    
    subgraph "缓存服务"
        RedisCache["对象缓存"] --> SessionCache["会话缓存"]
        RedisCache --> QueryCache["查询缓存"]
        RedisCache --> ApiCache["API 结果缓存"]
    end
    
    subgraph "存储服务"
        StorageManager["存储管理"] --> LocalStorage["本地存储"]
        StorageManager --> S3Storage["对象存储 (S3)"]
    end
    
    subgraph "消息队列"
        QueueManager["队列管理器"] --> RedisQueue["消息队列"]
        QueueManager --> ScheduledTasks["计划任务"]
    end
    
    subgraph "监控与日志"
        LogManager["日志管理"] --> AppLogs["应用日志"]
        LogManager --> AuditLogs["审计日志"]
        LogManager --> ErrorLogs["错误日志"]
    end
    
    Laravel --> RedisCache
    Laravel --> StorageManager
    Laravel --> QueueManager
    Laravel --> LogManager
```

## 12. 资源与兼容性

### 12.1 最低资源要求

| 资源类型 | 开发环境 | 生产环境 (小型) | 生产环境 (中型) |
|------------|----------|----------------|----------------|
| CPU        | 2核      | 4核            | 8核+            |
| 内存        | 4GB      | 8GB            | 16GB+           |
| 存储        | 20GB SSD | 50GB SSD       | 100GB+ SSD      |
| 数据库      | 5GB      | 20GB           | 50GB+           |
| 带宽        | 5Mbps    | 20Mbps         | 50Mbps+         |

### 12.2 兼容性矩阵

| 客户端类型 | 支持状态 | 最低要求版本 |
|------------|----------|------------|
| Chrome     | 完全支持   | 90+        |
| Edge       | 完全支持   | 90+        |
| Firefox    | 完全支持   | 90+        |
| Safari     | 完全支持   | 14+        |
| iOS Safari | 完全支持   | iOS 14+    |
| Android Chrome | 完全支持 | Android 8+ |

## 13. 扩展和集成点

```mermaid
graph TD
    EduSaaS["系统核心"] --> API["API 集成点"]
    
    API --> Mobile["移动应用"]
    API --> ThirdParty["第三方集成"]
    API --> ExternalAnalytics["外部分析工具"]
    
    subgraph "扩展模块"
        ExtensionSystem["扩展系统"]
        PluginA["插件 A"]
        PluginB["插件 B"]
    end
    
    EduSaaS --> ExtensionSystem
    ExtensionSystem --> PluginA
    ExtensionSystem --> PluginB
    
    subgraph "外部服务"
        SMS["短信服务"]
        Email["邮件服务"]
        Payment["支付系统"]
    end
    
    EduSaaS --> SMS
    EduSaaS --> Email
    EduSaaS --> Payment
```

## 14. 系统引导和开箱即用

```mermaid
flowchart LR
    A[租户注册] --> B[安装向导]
    B --> C[基本配置]
    C --> D[选择订阅计划]
    D --> E[业务模块设置]
    E --> F[导入模板数据]
    F --> G[规划架构教程]
    G --> H[系统就绪]
```

## 15. 系统与模块约束

**系统约束**：
- 每个子系统都应实现独立的服务接口与数据模型
- 模块间集成通过模块集成服务进行
- 全系统遵循同一的设计语言和规范

**模块约束**：
- 成绩分析模块与工资管理模块必须各自完整工作
- 模块可独立订阅使用
- 在订阅双模块时提供增强的集成功能