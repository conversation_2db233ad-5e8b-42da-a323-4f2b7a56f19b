# 技术栈架构文档

> **文档职责**: 本文档专注于教学事务管理系统的技术栈选型、组件架构和技术实现细节。
> 如需了解业务架构和模块关系，请参考 [业务架构概述](03-business-architecture-overview.md)；
> 如需了解域名和路由架构，请参考 [域名路由架构](07-domain-routing-architecture.md)；
> 如需了解具体实现细节，请参考 [开发文档/系统架构文档](../04-development-docs/09-system-architecture-document.md)。

## 1. 系统定位

教学事务管理系统是一款基于 SaaS (Software as a Service) 模式开发的多租户应用，基于 scoreDB25/saas 框架（tenancyforlaravel.com/saas-boilerplate v4 内部版本）构建，为学校提供全面的教学事务管理解决方案。系统采用模块化设计，支持学校、年级、班级、科目等层次的教学管理需求。

核心特点：

1. **基于成熟SaaS框架**：利用 scoreDB25/saas 框架的多租户、订阅、认证等成熟功能
2. **多租户架构**：支持多学校部署，每个学校作为独立租户，完全数据库隔离
3. **模块化设计**：功能划分为 Core、Score、Salary 三大独立模块
4. **定制化订阅**：租户可以根据需求订阅不同功能模块
5. **灵活的权限控制**：细粒度的角色和权限管理
6. **多域名架构**：主域名、管理后台、租户子域名完全隔离

## 2. 技术栈
本系统基于 scoreDB25/saas 框架构建，采用了现代化的技术栈，保证系统的可扩展性和稳定性：

### SaaS 框架层（已有）
- **后端框架**：Laravel 12
- **多租户实现**：stancl/tenancy for laravel 4.0（完全数据库隔离）
- **数据库**：PostgreSQL 17（每个租户独立数据库）
- **认证系统**：Laravel Session认证 + Socialite社交登录（不使用sanctum和passport）
- **订阅计费**：Laravel Cashier + Stripe（标准 subscriptions、subscription_items 表结构）
- **缓存系统**：Redis
- **文件存储**：S3 兼容存储（每租户隔离）
- **监控系统**：Laravel Telescope（开发）+ Sentry（生产）

### 应用扩展层（待开发）
- **前端框架**：Vue.js 3 + TypeScript + Vite
- **UI 组件库**：shadcn-vue（基于 Radix Vue + Tailwind CSS）
- **状态管理**：Pinia + Vue Query（服务器状态管理）
- **路由系统**：Vue Router 4
- **构建工具**：Vite 5
- **图表组件**：ECharts
- **表格组件**：Vue-table

### 开发环境
- **操作系统**：macOS
- **开发环境**：ddev
- **PHP版本**：8.4
- **数据库**：PostgreSQL 17
- **前端开发**：`ddev exec npm run dev`

### 部署架构
- **应用服务器**：Nginx + PHP-FPM
- **容器化**：Docker + Docker Compose
- **CI/CD**：GitHub Actions
- **监控**：Prometheus + Grafana

## 3. 系统架构图

```mermaid
flowchart TD
    subgraph "Front-End"
        UI["Vue.js + shadcn-vue"] --> Router["Vue Router"]
        Router --> Store["Pinia"]
        Store --> API["API Client"]
    end

    subgraph "Back-End"
        API --> MW["Middleware Layer"]
        MW --> Auth["Authentication"]
        MW --> Ten["Tenancy Manager"]
        MW --> Perm["Permission Check"]

        Ten --> TenDB[("Tenant Databases")]

        Auth --> Controllers
        Perm --> Controllers

        Controllers --> CoreM["Core Module"]
        Controllers --> ScoreM["Score Module"]
        Controllers --> SalaryM["Salary Module"]

        CoreM --> Models
        ScoreM --> Models
        SalaryM --> Models

        Models --> DB[("PostgreSQL")]
    end

    subgraph "Services"
        Controllers --> Cache[("Redis Cache")]
        Controllers --> Queue["Queue Worker"]
        Queue --> Jobs["Background Jobs"]
        Controllers --> Storage["File Storage"]
    end
```

## 4. 多租户架构

系统采用 stancl/tenancy 4.0 实现多租户架构，提供以下功能：

### 租户模型

- **中央数据库**：存储租户元数据，如租户信息、域名绑定、订阅状态等
- **租户数据库**：每个租户拥有一个独立的数据库，确保数据隔离

### 租户识别

系统支持多种租户识别方式：

1. **子域名识别**：通过不同子域名区分租户（例如：school1.example.com）
2. **路径识别**：通过URL路径识别租户（例如：example.com/tenant/school1）
3. **API 标识**：在 API 请求中通过标头或参数指定租户

### 资源隔离

- **数据存储**：每个租户的数据存储在独立的数据库中
- **文件存储**：每个租户的文件存储在独立的目录中
- **缓存隔离**：每个租户的缓存使用唯一前缀区分

## 5. 模块化设计

系统采用模块化设计，将功能划分为独立的功能模块：

### 核心模块 (Core)

所有租户默认包含的基础功能：

- 用户管理与身份验证
- 角色与权限管理
- 学校基础信息管理
- 部门与班级管理
- 系统配置和通用设置

### 成绩分析模块 (Score)

提供学生成绩管理与分析功能：

- 考试管理与成绩录入
- 学生成绩分析与可视化
- 班级成绩排名与分析
- 学科综合评估
- 成绩报表生成与导出

### 工资计算模块 (Salary)

提供教职工薪资管理功能：

- 工资结构配置与计算
- 工资单生成与导出
- 工资统计与分析
- 教师绩效评估集成
- 工资发放记录管理

### 模块交互

模块间通过以下方式进行交互：

- **事件系统**：模块可发布和监听事件
- **服务容器**：模块可注册和使用共享服务
- **数据关联**：模块可定义与其他模块的数据关联

## 6. 安全架构

系统采用多层次的安全架构：

### 身份验证

- **用户认证**：基于 Laravel Session 实现的 Cookie 认证和 Laravel Socialite 社交登录
- **多因素认证**：支持二次验证
- **单点登录**：支持多应用间的单点登录

### 权限控制

- **基于角色**：用户被分配特定角色
- **基于权限**：角色包含特定权限
- **模块访问控制**：访问只限于已订阅模块

### 数据安全

- **多租户隔离**：租户数据完全隔离
- **敬感数据加密**：敏感数据在存储时加密
- **安全审计**：记录所有敏感操作的审计日志

## 7. 扩展性设计

系统架构设计支持多方面的扩展：

### 模块扩展

- **定义新模块**：可以添加新的功能模块
- **自定义模块**：租户可根据需求自定义模块

### 性能扩展

- **水平扫展**：可增加应用服务器和数据库服务器
- **负载均衡**：支持多服务器部署和负载均衡

### API 集成

- **RESTful API**：所有功能都提供 RESTful API
- **Webhook**：支持外部系统集成
- **第三方认证**：支持 OAuth 等第三方登录

## 8. 数据流图

下面是系统的数据流图，展示主要组件间的数据流向：

```mermaid
flowchart LR
    subgraph "Users"
        Student["Student"]
        Teacher["Teacher"]
        Admin["Administrator"]
    end

    subgraph "Client"
        Browser["Web Browser"]
        MobileApp["Mobile App"]
    end

    subgraph "API Gateway"
        APIAuth["API Authentication"]
        TenantResolver["Tenant Resolver"]
        Routing["Request Routing"]
    end

    subgraph "Application"
        CoreApp["Core Application"]
        ScoreModule["Score Module"]
        SalaryModule["Salary Module"]
    end

    subgraph "Data Layer"
        CoreDB[("Core Database")]
        TenantDBs[("Tenant Databases")]
        Cache[("Redis Cache")]
        Storage["File Storage"]
    end

    Student --> Browser
    Teacher --> Browser
    Admin --> Browser
    Student --> MobileApp
    Teacher --> MobileApp

    Browser --> APIAuth
    MobileApp --> APIAuth

    APIAuth --> TenantResolver
    TenantResolver --> Routing

    Routing --> CoreApp
    Routing --> ScoreModule
    Routing --> SalaryModule

    CoreApp --> CoreDB
    CoreApp --> TenantDBs
    CoreApp --> Cache
    CoreApp --> Storage

    ScoreModule --> TenantDBs
    ScoreModule --> Cache

    SalaryModule --> TenantDBs
    SalaryModule --> Cache
```

## 9. 系统限制

当前系统架构有以下限制：

1. **并发用户数**：单个实例支持的最大并发用户数为 1000
2. **租户数量**：单个部署实例建议的最大租户数为 100
3. **数据存储**：每个租户的默认数据存储上限为 10GB
4. **文件存储**：每个租户的默认文件存储上限为 50GB

## 10. 未来升级路线

系统架构设计考虑了未来的扩展和升级：

1. **新功能模块**：如考勤管理、资产管理等
2. **人工智能集成**：添加智能成绩分析和预测
3. **移动应用**：开发原生移动应用
4. **国际化**：支持多语言和区域设置

## 11. 结论

教学事务管理系统采用现代化的多租户 SaaS 架构和模块化设计，提供了灵活、可扩展和安全的教学管理解决方案。这种设计保证了系统可以适应不同学校的需求，并允许它们根据需要选择特定功能模块。