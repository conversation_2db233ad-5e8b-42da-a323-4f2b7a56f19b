# 模块化架构

## 系统概述

教学事务管理系统采用模块化架构，将不同功能划分为独立的模块。这种设计允许定制化的租户订阅方案，每个租户可以选择订阅特定模块以满足其需求。

系统目前包含以下核心模块：

### 1. 核心模块 (core)

所有租户默认订阅的基础功能模块，提供：

- 用户管理和身份验证
- 角色与权限管理
- 学校基础信息管理
- 部门与班级管理
- 系统配置和通用设置

### 2. 成绩分析模块 (score)

为学校提供的成绩管理与分析功能，包括：

- 考试管理与成绩录入
- 学生成绩分析与可视化
- 班级成绩排名与分析
- 学科综合评估
- 成绩报表生成与导出

### 3. 工资计算模块 (salary)

为学校提供的教职工薪资管理功能，包括：

- 工资组成配置与计算
- 工资单生成与导出
- 工资统计与分析
- 教师绩效评估集成
- 工资发放记录管理

## 技术实现

系统基于 Laravel 12 和 stancl/tenancy 4.0 多租户架构实现，采用了完全模块化的设计理念：

### 后端模块化

- **数据库迁移**: 每个模块的迁移文件存放在独立目录中
- **路由组织**: 模块路由在 `routes/modules/` 目录中独立定义
- **模型分组**: 每个模块的模型在各自命名空间中组织
- **功能隔离**: 模块间通过清晰的接口进行交互

### 前端模块化

- **页面组织**: Vue页面按模块组织在 `resources/js/Pages/{ModuleName}/` 目录中
- **组件复用**: 通用组件在模块间共享
- **动态功能访问**: 基于模块订阅状态动态显示或隐藏功能

## 模块交互

虽然模块保持相对独立，但它们之间能够进行受控的集成和交互：

- **共享数据模型**: 核心模块提供的用户、班级等可被其他模块访问
- **事件集成**: 模块可以触发和响应事件，例如成绩分析可以影响工资计算
- **API集成点**: 每个模块提供标准化API用于其他模块调用

## 订阅管理

每个租户都有特定的模块订阅配置：

- **订阅管理**: 租户管理界面允许管理员配置租户订阅的模块
- **授权控制**: 使用 `EnsureModuleAccess` 中间件限制对非订阅模块的访问
- **数据隔离**: 仅为租户订阅的模块创建相应的数据库结构

## 好处与优势

这种模块化设计带来多项优势：

1. **灵活的商业模式**: 支持多层级订阅方案，满足不同学校的需求
2. **高效开发**: 开发团队可以并行开发不同模块
3. **更好的可维护性**: 独立模块降低了代码耦合度
4. **扩展性**: 新功能可以以新模块形式快速添加
5. **定制化**: 根据客户需求提供不同的功能组合

## 未来可扩展模块

系统架构设计允许我们未来扩展更多模块，可能的扩展方向包括：

- **考勤管理模块**: 教工考勤与签到管理
- **资产清单模块**: 学校资产管理与跟踪
- **教学评估模块**: 教学质量评估与反馈
- **学生行为管理模块**: 学生表现与品行评价

每个新模块都将遵循相同的模块化设计原则，确保系统的一致性和可维护性。