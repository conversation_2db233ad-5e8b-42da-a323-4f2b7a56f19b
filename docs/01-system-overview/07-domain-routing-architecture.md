# 域名和路由架构

## 概述
教学事务管理系统采用多域名架构，通过不同的域名实现功能隔离和安全控制。本文档详细说明系统的域名规划、路由架构和访问控制机制。

## 域名架构设计

> **域名灵活性说明**: 系统支持灵活的域名配置。本文档以 `scoredb25.ddev.site` 作为开发环境示例，生产环境可以使用任何域名，如 `goodsaleabcd.com`。系统会自动适配：
> - 主域名：`goodsaleabcd.com`
> - 管理后台：`hiddenadmin.goodsaleabcd.com`
> - 租户域名：`school1.goodsaleabcd.com`, `school2.goodsaleabcd.com` 等

### 1. 主域名（宣传和注册）
- **域名**: `{主域名}` 或 `www.{主域名}`
- **开发环境示例**: `scoredb25.ddev.site` 或 `www.scoredb25.ddev.site`
- **生产环境示例**: `goodsaleabcd.com` 或 `www.goodsaleabcd.com`
- **用途**: 系统宣传、用户注册、订阅购买
- **功能**:
  - 展示系统介绍和功能特性
  - 用户注册和登录
  - 订阅方案展示和购买
  - 子域名设置和管理
  - 订阅续费和管理

### 2. 系统管理后台
- **域名**: `hiddenadmin.{主域名}`
- **开发环境示例**: `hiddenadmin.scoredb25.ddev.site`
- **生产环境示例**: `hiddenadmin.goodsaleabcd.com`
- **用途**: 系统管理员专用后台
- **访问路径**: `/admin/login`
- **功能**:
  - 订阅用户管理
  - 订阅方案修订
  - 系统配置管理
  - 租户管理
  - 系统监控和统计

### 3. 租户域名（学校系统）
- **域名格式**: `{subdomain}.{主域名}`
- **开发环境示例**:
  - `default.scoredb25.ddev.site` - 默认租户
  - `test.scoredb25.ddev.site` - 测试租户
  - `school1.scoredb25.ddev.site` - 学校1
- **生产环境示例**:
  - `school1.goodsaleabcd.com` - 学校1
  - `school2.goodsaleabcd.com` - 学校2
- **用途**: 各学校的教学事务管理系统

## 登录和访问机制

### 租户登录方式
1. **直接登录**: `https://{subdomain}.scoredb25.ddev.site/login`
2. **主域名跳转**: `https://scoredb25.ddev.site/login#/{subdomain}`

### 登录流程说明
- 在主域名登录后，不会自动跳转到子域名
- 用户可以在主域名进行订阅管理、续费等操作
- 主域名登录后，可以自动登录到对应的租户子域名
- 反向登录（从子域名到主域名）不被支持

### 默认测试账户

#### 默认租户
- **URL**: `https://default.scoredb25.ddev.site/login`
- **邮箱**: `<EMAIL>`
- **密码**: `password`

#### 测试租户
- **URL**: `https://test.scoredb25.ddev.site/login`
- **邮箱**: `<EMAIL>`
- **密码**: `password`
- **特点**: 包含大量模拟数据

## 子域名管理规则

### 子域名设置规则
1. **唯一性**: 每个子域名在系统中必须唯一
2. **不可修改**: 子域名一旦设置确定，不允许修改
3. **字符限制**: 只允许字母、数字和连字符，不允许特殊字符
4. **长度限制**: 3-20个字符
5. **保留字**: 系统保留特定子域名（如 www、admin、api、hiddenadmin等）

### 子域名验证流程
```mermaid
flowchart TD
    A[用户输入子域名] --> B{格式验证}
    B -->|失败| C[提示格式错误]
    B -->|通过| D{唯一性检查}
    D -->|已存在| E[提示子域名已被使用]
    D -->|可用| F{保留字检查}
    F -->|是保留字| G[提示不能使用保留字]
    F -->|通过| H[创建租户和域名]
    H --> I[自动设置租户管理员]
```

## 路由架构

### 中央路由（主域名）
```php
// routes/web.php
Route::domain(config('app.central_domain'))->group(function () {
    // 宣传页面
    Route::get('/', 'HomeController@index');

    // 用户认证
    Route::get('/login', 'Auth\LoginController@showLoginForm');
    Route::post('/login', 'Auth\LoginController@login');
    Route::post('/logout', 'Auth\LoginController@logout');

    // 用户注册
    Route::get('/register', 'Auth\RegisterController@showRegistrationForm');
    Route::post('/register', 'Auth\RegisterController@register');

    // 订阅管理
    Route::middleware('auth')->group(function () {
        Route::get('/subscription', 'SubscriptionController@index');
        Route::post('/subscription/create', 'SubscriptionController@create');
        Route::get('/tenant/setup', 'TenantSetupController@index');
        Route::post('/tenant/setup', 'TenantSetupController@store');
    });
});
```

### 管理后台路由
```php
// routes/admin.php
Route::domain(config('app.admin_domain'))->group(function () {
    Route::prefix('admin')->group(function () {
        Route::get('/login', 'Admin\LoginController@showLoginForm');
        Route::post('/login', 'Admin\LoginController@login');

        Route::middleware('admin.auth')->group(function () {
            Route::get('/dashboard', 'Admin\DashboardController@index');
            Route::resource('/tenants', 'Admin\TenantController');
            Route::resource('/subscriptions', 'Admin\SubscriptionController');
            Route::resource('/plans', 'Admin\PlanController');
        });
    });
});
```

### 租户路由
```php
// routes/tenant.php
Route::middleware([
    'web',
    InitializeTenancyByDomainOrSubdomain::class,
    PreventAccessFromUnwantedDomains::class,
])->group(function () {
    // 租户认证
    Route::get('/login', 'Tenant\Auth\LoginController@showLoginForm');
    Route::post('/login', 'Tenant\Auth\LoginController@login');

    // 租户功能路由
    Route::middleware('auth')->group(function () {
        Route::get('/dashboard', 'Tenant\DashboardController@index');

        // 核心模块路由
        Route::prefix('core')->name('core.')->group(function () {
            Route::resource('users', 'Tenant\Core\UserController');
            Route::resource('classes', 'Tenant\Core\ClassController');
            Route::resource('grades', 'Tenant\Core\GradeController');
        });

        // 成绩模块路由（需要订阅验证）
        Route::middleware('ensure.module.access:score')
            ->prefix('score')
            ->name('score.')
            ->group(function () {
                Route::resource('exams', 'Tenant\Score\ExamController');
                Route::resource('scores', 'Tenant\Score\ScoreController');
                Route::get('analysis', 'Tenant\Score\AnalysisController@index');
            });

        // 工资模块路由（需要订阅验证）
        Route::middleware('ensure.module.access:salary')
            ->prefix('salary')
            ->name('salary.')
            ->group(function () {
                Route::resource('salaries', 'Tenant\Salary\SalaryController');
                Route::get('reports', 'Tenant\Salary\ReportController@index');
            });
    });
});
```

## 安全控制

### 域名访问控制
1. **中央域名保护**: 防止租户域名访问中央功能
2. **租户隔离**: 确保租户只能访问自己的数据
3. **管理后台隐藏**: 使用不可猜测的域名保护管理后台

### 中间件保护
- `InitializeTenancyByDomainOrSubdomain`: 租户识别和初始化
- `PreventAccessFromUnwantedDomains`: 防止非法域名访问
- `ensure.module.access`: 模块订阅验证
- `admin.auth`: 管理员身份验证

## ddev 环境配置

### 配置文件设置
```yaml
# .ddev/config.yaml
name: scoredb25
type: laravel
docroot: public
php_version: "8.4"
database:
  type: postgres
  version: "17"

additional_hostnames:
  - hiddenadmin.scoredb25
  - "*.scoredb25"  # 支持所有子域名通配符

additional_fqdns:
  - scoredb25.ddev.site
  - www.scoredb25.ddev.site
  - hiddenadmin.scoredb25.ddev.site
  - default.scoredb25.ddev.site
  - test.scoredb25.ddev.site
```

### 环境变量配置
```bash
# .env - 开发环境示例
APP_URL="https://scoredb25.ddev.site"
APP_DOMAIN="scoredb25.ddev.site"
ADMIN_DOMAIN="hiddenadmin.scoredb25.ddev.site"

# 生产环境示例
# APP_URL="https://goodsaleabcd.com"
# APP_DOMAIN="goodsaleabcd.com"
# ADMIN_DOMAIN="hiddenadmin.goodsaleabcd.com"

# Tenancy配置 - 根据实际域名调整
TENANCY_CENTRAL_DOMAINS="${APP_DOMAIN},www.${APP_DOMAIN},${ADMIN_DOMAIN}"
```

### 配置文件设置
```php
// config/app.php
'central_domain' => env('APP_DOMAIN', 'scoredb25.ddev.site'),
'admin_domain' => env('ADMIN_DOMAIN', 'hiddenadmin.scoredb25.ddev.site'),

// config/tenancy.php
'central_domains' => explode(',', env('TENANCY_CENTRAL_DOMAINS', 'scoredb25.ddev.site')),
```

## 最佳实践

### 开发建议
1. **域名一致性**: 确保所有配置文件中的域名保持一致
2. **测试覆盖**: 为每种域名类型编写测试用例
3. **错误处理**: 为域名解析失败提供友好的错误页面
4. **性能优化**: 使用DNS缓存减少域名解析时间

### 部署注意事项
1. **SSL证书**: 为所有域名配置SSL证书
2. **DNS配置**: 正确配置通配符DNS记录
3. **负载均衡**: 考虑多服务器部署时的域名路由
4. **监控告警**: 监控域名解析和访问状态

## 故障排除

### 常见问题
1. **子域名无法访问**: 检查DNS配置和ddev设置
2. **租户识别失败**: 验证tenancy中间件配置
3. **跨域问题**: 检查CORS配置
4. **SSL证书错误**: 确认证书覆盖所有子域名

### 调试命令
```bash
# 检查路由列表
php artisan route:list

# 检查租户列表
php artisan tenants:list

# 测试域名解析
nslookup test.scoredb25.ddev.site

# 检查ddev状态
ddev describe
```
