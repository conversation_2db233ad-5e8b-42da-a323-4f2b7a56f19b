# 教学事务管理系统介绍

## 产品定位
教学事务管理系统是基于 scoreDB25/saas 框架（tenancyforlaravel.com/saas-boilerplate v4 内部版本）开发的多租户 SaaS 应用，采用 Laravel 12 + PostgreSQL + shadcn-vue 技术栈，在现有成熟的 SaaS 框架基础上扩展教学管理功能。系统主要包括成绩分析系统和工资计算查询系统两大核心模块，采用模块化订阅机制，学校租户可根据需求选择订阅单一模块或完整系统。

## 系统域名架构
系统采用多域名架构，实现不同功能的完全隔离。支持灵活的域名配置，可适应不同的部署环境：

> **域名灵活性**: 系统支持任意主域名，如开发环境使用 `scoredb25.ddev.site`，生产环境可使用 `goodsaleabcd.com` 等。

### 主域名（宣传和注册）
- **格式**: `https://{主域名}` 或 `https://www.{主域名}`
- **开发环境示例**: `https://scoredb25.ddev.site` 或 `https://www.scoredb25.ddev.site`
- **生产环境示例**: `https://goodsaleabcd.com` 或 `https://www.goodsaleabcd.com`
- **功能**: 系统宣传页面、用户注册、订阅购买、子域名设置
- **特点**: 用户注册后可购买订阅并设置子域名，子域名一旦确定不可修改

### 系统管理后台
- **格式**: `https://hiddenadmin.{主域名}/admin/login`
- **开发环境示例**: `https://hiddenadmin.scoredb25.ddev.site/admin/login`
- **生产环境示例**: `https://hiddenadmin.goodsaleabcd.com/admin/login`
- **功能**: 系统管理、订阅用户管理、订阅方案修订、系统配置
- **特点**: 仅供系统管理员使用，不对订阅用户开放，默认不提供注册功能

### 租户域名（学校系统）
- **格式**: `https://{subdomain}.{主域名}`
- **开发环境示例**:
  - 默认租户：`https://default.scoredb25.ddev.site/login`（<EMAIL> / password）
  - 测试租户：`https://test.scoredb25.ddev.site/login`（<EMAIL> / password）
- **生产环境示例**:
  - 学校1：`https://school1.goodsaleabcd.com/login`
  - 学校2：`https://school2.goodsaleabcd.com/login`
- **功能**: 各学校的教学事务管理系统
- **登录方式**:
  - 直接登录：`https://{subdomain}.{主域名}/login`
  - 主域名跳转：`https://{主域名}/login#/{subdomain}`

## 架构基础
本系统基于已有的 scoreDB25/saas 框架构建，该框架提供了：
- **成熟的多租户架构**：基于 stancl/tenancy for laravel 4.0 的完全数据库隔离
- **订阅计费系统**：集成 Laravel Cashier + Stripe 的标准订阅模型
- **用户认证授权**：完整的 RBAC 权限管理系统，不使用sanctum和passport
- **租户管理**：自动化的租户初始化和配置
- **域名管理**：子域名和自定义域名支持
- **基础 UI 框架**：预配置的前端基础设施

我们在此框架基础上扩展教学管理相关的业务功能，确保与框架解耦，便于框架升级时的兼容性维护。

## 核心价值
- **基于成熟 SaaS 框架**：利用 scoreDB25/saas 框架的多租户、订阅、认证等成熟功能，专注于教学业务逻辑开发
- **多域名支持**：继承框架的子域名识别（如 school1.scoredb25.ddev.site）和自定义域名绑定能力
- **现代化无障碍界面**：使用 shadcn-vue 构建基于 Radix Vue 和 Tailwind CSS 的美观、响应式、可访问的用户界面，符合 Web 无障碍标准
- **全角色覆盖**：满足学生、家长、教师、班主任、年级主任、学科组长、校领导等不同角色的需求
- **深度分析**：提供从个人到班级、年级的多维度成绩分析
- **工资管理**：支持复杂薪资结构的计算、查询和统计
- **数据可视化**：直观展示成绩趋势、薪资变化和统计分析结果
- **模块化订阅**：基于 Laravel Cashier 的标准订阅系统，支持按模块授权和计费

## 技术栈
### SaaS 框架层（已有）
- **后端框架**：Laravel 12
- **多租户**：stancl/tenancy for laravel 4.0（完全数据库隔离）
- **数据库**：PostgreSQL 17（每个租户独立数据库）
- **认证**：Laravel Session认证 + Socialite社交登录（不使用sanctum和passport）
- **订阅计费**：Laravel Cashier + Stripe（标准 subscriptions、subscription_items 表结构）
- **缓存**：Redis
- **文件存储**：S3 兼容存储（每租户隔离）
- **监控**：Laravel Telescope（开发）+ Sentry（生产）

### 应用扩展层（待开发）
- **前端框架**：Vue.js 3 + TypeScript + Vite
- **UI 组件**：shadcn-vue（基于 Radix Vue + Tailwind CSS）
- **状态管理**：Pinia + Vue Query（服务器状态管理）
- **路由**：Vue Router 4
- **构建工具**：Vite 5
- **部署**：Docker + Nginx + Redis

### 开发环境
- **操作系统**：macOS
- **开发环境**：ddev
- **PHP版本**：8.4
- **数据库**：PostgreSQL 17
- **前端开发命令**：`ddev exec npm run dev`

## 模块化架构
系统采用三大模块设计，支持独立开发和订阅：

### Core 模块（核心模块）
所有租户默认包含的基础功能：
- 用户管理与身份验证
- 角色与权限管理（教师、班主任、年级主任、学生、家长、工作人员、校长、教务主任、德育主任等）
- 学校基础信息管理
- 部门管理（教务处、德育处、总务处、办公室等）
- 年级和班级管理
- 学科管理（语文、数学、英语、物理、化学、生物、政治、历史、地理等）
- 系统配置和通用设置

### Score 模块（成绩分析模块）
提供学生成绩管理与分析功能：
- 考试管理（月考、期中、期末、单元测试、模拟考试等）
- 成绩录入与管理
- 多维度成绩分析（班级、年级、学科、考试类型等）
- 学生个人成绩趋势分析
- 学科强弱项分析
- 教师教学质量评估
- 成绩报表生成与导出
- 数据可视化展示

### Salary 模块（工资计算模块）
提供教职工薪资管理功能：
- 教职工信息管理
- 工资结构配置与计算
- 工资单生成与导出
- 工资统计与分析
- 教师绩效评估集成
- 工资发放记录管理

## 开发优先级
### 第一阶段：成绩分析系统（优先开发）
- 成绩录入与管理
- 考试管理
- 多维度成绩分析
- 学情报告生成
- 成绩趋势追踪
- 数据可视化展示

### 第二阶段：工资计算查询系统（暂不开发）
- 教职工信息管理
- 薪资组成配置
- 薪资计算自动化
- 薪资查询与导出
- 薪资统计分析

## 订阅模型（基于框架标准）
利用 scoreDB25/saas 框架的 Laravel Cashier 集成，扩展教学管理相关的订阅计划：

### 订阅计划设计
1. **基础版**（Stripe 价格 ID：price_basic_monthly）
   - 框架核心功能（用户管理、租户设置等）
   - 基础教学数据管理（学生、教师、班级信息）
   - 月付：¥299/月，年付：¥2,990/年（优惠17%）

2. **成绩分析版**（Stripe 价格 ID：price_score_monthly）
   - 基础版 + 成绩分析系统完整功能
   - 成绩录入、分析、报告生成
   - 学生、班级和年级数据分析
   - 月付：¥599/月，年付：¥5,990/年（优惠17%）

3. **工资管理版**（Stripe 价格 ID：price_salary_monthly）
   - 基础版 + 工资计算查询系统完整功能
   - 薪资配置、计算、查询统计
   - 工资单生成和管理
   - 月付：¥799/月，年付：¥7,990/年（优惠17%）

4. **完整版**（Stripe 价格 ID：price_complete_monthly）
   - 包含所有模块的全部功能
   - 成绩与薪资的综合分析
   - 高级数据导出和报表
   - 月付：¥1,299/月，年付：¥12,990/年（优惠17%）

### 订阅集成机制
- **利用现有表结构**：基于框架的 `subscriptions` 和 `subscription_items` 表
- **Tenant 模型集成**：使用框架 Tenant 模型的 Billable trait
- **中间件授权**：扩展框架的权限中间件，支持模块级别访问控制
- **订阅状态检查**：集成框架的订阅状态检查机制

## 系统特点
1. **基于成熟框架**：
   - 利用 scoreDB25/saas 框架的多租户、订阅、认证等核心功能
   - 专注于教学业务逻辑开发，避免重复造轮子
   - 与框架解耦设计，确保框架升级兼容性

2. **多租户 SaaS 支持**：
   - 继承框架的完全数据库隔离
   - 支持子域名和自定义域名
   - 自动化租户初始化和配置

3. **成绩管理**（优先开发）：
   - 灵活的成绩导入导出（Excel/CSV）
   - 批量导入/单条录入
   - 成绩审核流程
   - 多维度成绩分析报告

4. **工资管理**（第二阶段）：
   - 灵活的薪资组成配置
   - 多种计算方式支持（固定值/百分比/公式）
   - 历史薪资查询
   - 薪资报表与统计

5. **角色化视图**：
   - 学生：个人成绩和排名
   - 家长：子女学习情况
   - 教师：学科教学分析和个人薪资查询
   - 班主任：班级综合管理
   - 行政人员：薪资管理和报表
   - 校领导：全校数据概览

6. **现代化前端体验**：
   - 基于 shadcn-vue 的现代化组件
   - 支持暗黑模式和主题定制
   - 响应式设计，支持移动端访问
   - 符合 Web 无障碍标准（WCAG 2.1）

7. **数据安全**：
   - 继承框架的数据加密传输（HTTPS）
   - 基于角色的访问控制(RBAC)
   - 操作审计日志
   - 敏感数据保护
   - 数据库级别完全隔离

8. **SaaS 运营支持**：
   - 利用框架的在线订阅管理和支付
   - 自动化账单和发票
   - 使用情况统计和分析
   - 客户支持和工单系统
   - 自动化备份和灾难恢复

## 开发原则
1. **框架兼容性**：确保扩展功能与 scoreDB25/saas 框架解耦，便于框架升级
2. **模块化设计**：成绩分析和工资管理系统独立开发，支持独立订阅
3. **标准集成**：利用框架的标准接口和扩展点，避免修改框架核心代码
4. **渐进式开发**：优先开发成绩分析系统，验证架构设计后扩展工资管理功能
