# 子域名分离安全方案实施指南

## 🎯 目标

1. **租户子域名登录**: `{tenant}.scoredb25.ddev.site/login`
2. **隐藏管理员登录**: `hiddenadmin.scoredb25.ddev.site/login`
3. **完全隔离**: 避免租户域名被猜测

## 📋 实施步骤

### 步骤1: 修改环境配置

```bash
# .env
APP_URL="https://hiddenadmin.scoredb25.ddev.site"
CENTRAL_ADMIN_DOMAIN="hiddenadmin.scoredb25.ddev.site"
```

### 步骤2: 更新tenancy配置

```php
// config/tenancy.php
'central_domains' => [
    env('CENTRAL_ADMIN_DOMAIN', 'hiddenadmin.scoredb25.ddev.site'),
],
```

### 步骤3: 配置ddev多域名支持

```yaml
# .ddev/config.yaml
additional_hostnames:
  - hiddenadmin.scoredb25
  - "*.scoredb25"  # 支持所有子域名
```

### 步骤4: 更新数据库中的租户域名

```php
// 将现有租户改为子域名格式
DB::table('domains')->where('domain', 'scoredb25.ddev.site')
  ->update(['domain' => 'bac.scoredb25.ddev.site']);
```

### 步骤5: 修改租户创建逻辑

```php
// CreateTenantAction.php 或相关seeder
// 新租户自动创建为子域名格式
$domain = $tenantId . '.scoredb25.ddev.site';
```

## 🔄 访问流程

### 租户登录流程
```
用户访问: https://bac.scoredb25.ddev.site/login
↓
租户中间件: 识别到tenant=bac
↓
显示: 租户登录页面 (AuthenticatedSessionController)
```

### 管理员登录流程
```
管理员访问: https://hiddenadmin.scoredb25.ddev.site/login
↓
中央域名: 匹配central_domains配置
↓
显示: 中央登录页面 (LoginTenantController)
```

## 🛡️ 安全优势

1. **域名隐藏**: 管理员登录域名不可猜测
2. **租户隔离**: 每个租户只能访问自己的子域名
3. **访问控制**: 无法通过中央登录发现其他租户
4. **DNS级别分离**: 完全的网络层隔离

## 📝 需要修改的文件

1. `.env` - 添加中央管理域名
2. `.ddev/config.yaml` - 添加域名支持
3. `config/tenancy.php` - 更新central_domains
4. 数据库 - 更新现有域名记录
5. `database/seeders/TenantSeeder.php` - 使用子域名格式

## 🚀 实施难度评估

- **配置修改**: ⭐ 简单
- **代码修改**: ⭐ 最小
- **数据迁移**: ⭐⭐ 中等
- **测试工作**: ⭐⭐ 中等
- **总体难度**: **很低** 🟢

## ✅ 兼容性

- ✅ 完全兼容现有tenancyforlaravel.com框架
- ✅ 不影响现有业务逻辑
- ✅ 向后兼容（可逐步迁移）
- ✅ 支持将来扩展更多中央域名 