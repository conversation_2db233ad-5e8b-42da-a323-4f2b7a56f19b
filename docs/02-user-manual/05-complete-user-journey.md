# 完整用户流程指南

## 1. 概述

本文档描述了 EduSaaS 教学事务管理系统的完整用户流程，从学校注册订阅到师生日常使用的全流程指导。系统基于 SaaS 多租户架构，每个学校都是独立的租户，拥有独立的数据空间和访问域名。

## 2. 学校注册与订阅流程

### 2.1 学校管理员注册

#### 2.1.1 访问注册页面
1. 访问 EduSaaS 官方网站：https://edusaas.com
2. 点击"免费试用"或"立即注册"按钮
3. 进入学校注册表单页面

#### 2.1.2 填写学校信息
注册表单包含以下必填信息：
- **学校名称**：完整的学校名称
- **学校代码**：用于生成子域名（如：school1）
- **管理员姓名**：学校系统管理员姓名
- **管理员邮箱**：用于接收系统通知
- **管理员手机号**：用于账号安全验证
- **学校地址**：学校完整地址
- **学校规模**：学生人数范围选择

#### 2.1.3 选择订阅计划
注册时可选择以下计划：
- **基础版**：¥299/月 - 基础功能
- **成绩分析版**：¥599/月 - 基础功能 + 成绩分析
- **工资管理版**：¥799/月 - 基础功能 + 工资管理  
- **完整版**：¥1,299/月 - 全部功能

> 所有计划都提供 14 天免费试用期

#### 2.1.4 完成注册
1. 确认服务条款和隐私政策
2. 点击"创建学校账户"
3. 系统自动：
   - 创建独立的租户数据库
   - 生成学校专用访问域名（如：school1.edusaas.com）
   - 发送确认邮件
   - 初始化基础数据结构

### 2.2 系统初始化设置

#### 2.2.1 首次登录
1. 收到注册成功邮件后，点击激活链接
2. 访问学校专用域名进行首次登录
3. 使用注册时设置的管理员账号登录

#### 2.2.2 学校基础信息配置
1. **学校设置**：
   - 学校 Logo 上传
   - 学校简介和联系方式
   - 学年学期设置
   - 课程表时间安排

2. **系统参数配置**：
   - 成绩等级标准
   - 考试类型定义
   - 班级年级结构
   - 用户角色权限

#### 2.2.3 组织架构搭建
按以下顺序创建组织架构：
1. **年级设置**：如高一、高二、高三
2. **班级创建**：每个年级下的具体班级
3. **科目设置**：各年级的教学科目
4. **部门设置**：教务处、人事处等管理部门

## 3. 用户账号管理流程

### 3.1 批量导入用户

#### 3.1.1 准备用户数据
下载系统提供的用户导入模板，包含：
- **学生信息模板**：学号、姓名、班级、联系方式等
- **教师信息模板**：工号、姓名、科目、部门等
- **管理员模板**：账号、姓名、角色、权限等

#### 3.1.2 数据导入流程
1. 在"用户管理" > "批量导入"页面
2. 选择要导入的用户类型
3. 上传填写好的 Excel 文件
4. 系统验证数据格式和完整性
5. 预览导入结果
6. 确认导入，系统自动：
   - 创建用户账号
   - 生成初始密码
   - 分配相应角色权限
   - 发送账号信息（如设置）

### 3.2 单个用户创建
对于少量用户，可使用单个创建功能：
1. 在相应的用户管理页面点击"添加用户"
2. 填写用户基本信息
3. 设置角色和权限
4. 设置初始密码或让系统生成
5. 保存创建

## 4. 订阅功能配置流程

### 4.1 成绩分析模块配置

> 此部分仅适用于订阅了"成绩分析版"或"完整版"的学校

#### 4.1.1 考试设置
1. **考试类型定义**：
   - 月考、期中考、期末考
   - 单元测试、模拟考试
   - 设置各类型考试的权重

2. **考试创建流程**：
   - 设置考试名称和时间
   - 选择参与年级和班级
   - 设置考试科目和分值
   - 配置成绩录入权限

#### 4.1.2 成绩录入配置
1. **录入权限设置**：
   - 任课教师只能录入所教科目
   - 班主任可以查看班级全科成绩
   - 年级主任可以管理年级所有成绩

2. **审核流程配置**：
   - 设置成绩录入后的审核环节
   - 配置审核人员和审核时限
   - 设置成绩发布的条件

### 4.2 工资管理模块配置

> 此部分仅适用于订阅了"工资管理版"或"完整版"的学校

#### 4.2.1 薪资结构设置
1. **基础工资配置**：
   - 不同职位的基础工资标准
   - 工龄工资计算规则
   - 职务津贴标准

2. **绩效工资设置**：
   - 教学绩效评估标准
   - 管理岗位绩效指标
   - 绩效工资计算公式

#### 4.2.2 工资计算规则
1. **扣除项目设置**：
   - 五险一金缴费比例
   - 个人所得税计算方式
   - 其他扣减项目

2. **奖金和补贴**：
   - 年终奖发放规则
   - 节日补贴标准
   - 加班费计算方式

## 5. 日常使用流程

### 5.1 教师使用流程

#### 5.1.1 成绩录入流程（成绩分析模块）
1. **考试前准备**：
   - 查看考试安排和科目信息
   - 确认班级学生名单
   - 准备成绩录入表格

2. **成绩录入**：
   - 登录系统选择相应考试
   - 逐个录入学生成绩或批量导入
   - 检查录入数据的准确性
   - 提交成绩等待审核

3. **成绩分析**：
   - 查看班级成绩统计
   - 分析学生成绩分布
   - 生成科目教学报告

#### 5.1.2 个人工资查询（工资管理模块）
1. **工资查询**：
   - 登录后查看当月工资明细
   - 查看历史工资记录
   - 下载个人工资单

2. **绩效查看**：
   - 查看个人教学绩效评分
   - 了解绩效工资计算依据
   - 查看绩效改进建议

### 5.2 学生使用流程

详细的学生使用流程请参考《学生使用指南》文档，主要包括：
1. 首次登录和密码修改
2. 个人信息完善
3. 成绩查看和分析
4. 学情报告查看
5. 家长账号绑定

### 5.3 家长使用流程

#### 5.3.1 账号获取
家长通过以下方式获取查看权限：
1. **学生授权方式**：
   - 学生在个人设置中绑定家长手机号
   - 家长接收验证码确认绑定
   - 获得查看学生成绩的权限

2. **学校统一分配**：
   - 学校批量创建家长账号
   - 通过短信或微信发送账号信息
   - 家长首次登录修改密码

#### 5.3.2 家长查看功能
1. **成绩查看**：
   - 查看孩子的各科成绩
   - 查看班级和年级排名
   - 查看成绩变化趋势

2. **学情了解**：
   - 查看教师对孩子的评语
   - 了解学习建议和改进方向
   - 查看学情分析报告

## 6. 系统管理流程

### 6.1 日常维护

#### 6.1.1 用户管理
- 定期检查用户账号状态
- 处理密码重置请求
- 管理用户权限变更
- 清理不活跃账号

#### 6.1.2 数据备份
- 系统自动每日备份数据
- 管理员可手动触发备份
- 定期验证备份数据完整性
- 制定数据恢复预案

### 6.2 订阅管理

#### 6.2.1 订阅升级
当学校需要更多功能时：
1. 在"订阅管理"页面查看当前计划
2. 选择要升级的目标计划
3. 确认价格差异和支付方式
4. 完成支付后功能立即生效

#### 6.2.2 订阅降级
如需降级订阅：
1. 联系客服申请降级
2. 确认数据保留策略
3. 在下个计费周期生效
4. 相关功能将被限制访问

## 7. 客户支持流程

### 7.1 技术支持
用户遇到问题时的支持渠道：
1. **在线帮助文档**：系统内置的帮助中心
2. **工单系统**：提交技术支持请求
3. **电话支持**：工作时间内的电话咨询
4. **远程协助**：必要时的远程技术支持

### 7.2 培训服务
- **新用户培训**：注册后的免费培训服务
- **功能培训**：新功能发布时的使用培训
- **管理员培训**：系统管理和维护培训
- **最佳实践分享**：定期的用户经验分享会

## 8. 数据安全与隐私

### 8.1 数据保护措施
- **数据库隔离**：每个学校独立的数据库
- **传输加密**：HTTPS 协议保护数据传输
- **访问控制**：严格的用户权限管理
- **审计日志**：完整的操作记录

### 8.2 隐私保护
- **最小权限原则**：用户只能访问必要的数据
- **数据匿名化**：统计分析时的数据脱敏
- **家长同意**：学生信息的家长授权机制
- **数据导出控制**：限制敏感数据的导出

## 9. 系统升级与更新

### 9.1 功能更新
- **自动更新**：系统功能的自动升级
- **更新通知**：重要更新的提前通知
- **功能预览**：新功能的测试预览
- **回滚机制**：问题更新的快速回滚

### 9.2 用户反馈
- **功能建议**：用户提出的功能改进建议
- **Bug 反馈**：问题报告和处理流程
- **满意度调查**：定期的用户满意度调查
- **用户大会**：年度用户交流和反馈会议

## 10. 最佳实践建议

### 10.1 实施建议
- **分阶段实施**：建议先从基础功能开始，逐步扩展
- **充分培训**：确保管理员和核心用户充分了解系统
- **数据准备**：提前整理和标准化用户数据
- **试运行**：正式启用前进行充分的试运行测试

### 10.2 使用优化
- **定期检查**：定期检查系统使用情况和效果
- **用户反馈**：建立用户反馈收集机制
- **持续改进**：根据实际使用情况持续优化配置
- **经验分享**：与其他学校分享使用经验和最佳实践

通过遵循以上完整的用户流程，学校可以顺利实施和使用 EduSaaS 教学事务管理系统，实现教学管理的数字化和智能化。 