# API概览（更新版）

## 1. API架构

scoreDB25系统基于Laravel 12 + stancl/tenancy 4.0实现了完整的多租户API架构，提供RESTful API支持移动端和第三方集成。API使用数据组JSON格式鎚返回数据，支持CORS跨域请求，为不同类型的客户端集成提供便利。

## 2. API访问基础

### 2.1 基础URL

所有API请求必须包含租户信息，采用以下两种方式之一：

1. **子域名模式**：
   ```
   https://tenant-id.example.com/api/v1/{endpoint}
   ```

2. **路径模式**：
   ```
   https://example.com/api/tenant/{tenant-id}/v1/{endpoint}
   ```

### 2.2 资源格式

- API返回的数据格式为JSON
- 请求支持的内容类型：`application/json`
- 必要时返回适当的HTTP状态码

## 3. 认证机制

### 3.1 Laravel Session认证

scoreDB25采用Laravel Session进行Web API认证：

#### 认证流程：

1. **用户登录**：通过传统登录或社交登录获得session
2. **Session验证**：API请求通过web中间件验证session
3. **CSRF保护**：自动处理CSRF token验证

#### 认证示例：

```javascript
// 前端API调用（自动包含session和CSRF token）
const response = await fetch('/api/user', {
    credentials: 'include', // 包含cookies
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    }
});
```

#### 优势：

- **简单高效**：无需管理token生命周期
- **安全性高**：HttpOnly cookies防止XSS攻击
- **自动处理**：Laravel自动处理session管理
- **CSRF保护**：内置CSRF保护机制

## 4. 成绩分析API

### 4.1 考试管理

#### 4.1.1 获取考试列表

```http
GET /api/v1/exams
Authorization: Bearer {token}
```

参数：
- `academic_year` - 可选，学年筛选
- `semester` - 可选，学期筛选
- `page` - 页数
- `per_page` - 每页条数

响应：

```json
{
    "data": [
        {
            "id": 1,
            "name": "2024年期中考试",
            "exam_type": "mid_term",
            "start_date": "2024-05-10",
            "end_date": "2024-05-15",
            "academic_year": "2023-2024",
            "semester": "spring",
            "is_published": true,
            "published_at": "2024-05-20T08:30:00Z"
        },
        {...}
    ],
    "links": {
        "first": "https://api.example.com/api/v1/exams?page=1",
        "last": "https://api.example.com/api/v1/exams?page=5",
        "prev": null,
        "next": "https://api.example.com/api/v1/exams?page=2"
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "path": "https://api.example.com/api/v1/exams",
        "per_page": 15,
        "to": 15,
        "total": 68
    }
}
```

#### 4.1.2 创建新考试

```http
POST /api/v1/exams
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "2024年期末考试",
    "exam_type": "final",
    "start_date": "2024-07-01",
    "end_date": "2024-07-05",
    "academic_year": "2023-2024",
    "semester": "spring",
    "subjects": [
        {
            "subject_id": 1,
            "exam_date": "2024-07-01",
            "start_time": "09:00",
            "end_time": "11:00",
            "full_score": 100,
            "pass_score": 60
        },
        {...}
    ]
}
```

#### 4.1.3 获取考试详情

```http
GET /api/v1/exams/{exam_id}
Authorization: Bearer {token}
```

### 4.2 成绩管理

#### 4.2.1 获取成绩列表

```http
GET /api/v1/exams/{exam_id}/scores
Authorization: Bearer {token}
```

参数：
- `subject_id` - 可选，筛选科目
- `class_id` - 可选，筛选班级
- `min_score` - 可选，最低分数
- `max_score` - 可选，最高分数

#### 4.2.2 导入成绩

```http
POST /api/v1/exams/{exam_id}/subjects/{subject_id}/scores/import
Authorization: Bearer {token}
Content-Type: application/json

{
    "scores": [
        {
            "student_id": "S2024001",
            "score": 85.5,
            "is_absent": false,
            "is_makeup": false,
            "comment": "表现不错"
        },
        {...}
    ]
}
```

#### 4.2.3 获取学生成绩

```http
GET /api/v1/students/{student_id}/scores
Authorization: Bearer {token}
```

参数：
- `exam_id` - 可选，筛选考试
- `subject_id` - 可选，筛选科目
- `academic_year` - 可选，学年筛选

### 4.3 成绩分析

#### 4.3.1 班级成绩分析

```http
GET /api/v1/analytics/exams/{exam_id}/classes/{class_id}/subjects/{subject_id}
Authorization: Bearer {token}
```

响应：

```json
{
    "data": {
        "exam_id": 1,
        "class_id": 5,
        "subject_id": 2,
        "student_count": 42,
        "avg_score": 76.8,
        "max_score": 98.5,
        "min_score": 45.0,
        "pass_count": 38,
        "pass_rate": 90.48,
        "excellent_count": 12,
        "excellent_rate": 28.57,
        "score_distribution": {
            "0-10%": 0,
            "10-20%": 0,
            "20-30%": 0,
            "30-40%": 0,
            "40-50%": 2,
            "50-60%": 2,
            "60-70%": 8,
            "70-80%": 18,
            "80-90%": 10,
            "90-100%": 2
        }
    }
}
```

#### 4.3.2 学生成绩趋势

```http
GET /api/v1/analytics/students/{student_id}/subjects/{subject_id}/trends
Authorization: Bearer {token}
```

参数：
- `limit` - 可选，返回的考试数量，默认5

#### 4.3.3 生成学生报告

```http
POST /api/v1/reports/students/{student_id}/exams/{exam_id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "format": "pdf",
    "include_charts": true,
    "include_trends": true
}
```

## 5. 工资管理API

### 5.1 员工管理

#### 5.1.1 获取员工列表

```http
GET /api/v1/employees
Authorization: Bearer {token}
```

参数：
- `department_id` - 可选，部门筛选
- `position` - 可选，职位筛选
- `is_active` - 可选，活跃状态

#### 5.1.2 获取员工详情

```http
GET /api/v1/employees/{employee_id}
Authorization: Bearer {token}
```

### 5.2 薪资组成管理

#### 5.2.1 获取薪资组成项

```http
GET /api/v1/salary-components
Authorization: Bearer {token}
```

参数：
- `type` - 可选，类型筛选 (addition/deduction)
- `is_active` - 可选，活跃状态

#### 5.2.2 添加员工薪资组成

```http
POST /api/v1/employees/{employee_id}/salary-components
Authorization: Bearer {token}
Content-Type: application/json

{
    "salary_component_id": 5,
    "override_value": "1500",
    "starts_at": "2024-06-01",
    "ends_at": null
}
```

### 5.3 薪资计算

#### 5.3.1 计算员工薪资

```http
POST /api/v1/salary-calculations/employees/{employee_id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "period": "2024-06"
}
```

#### 5.3.2 批量计算薪资

```http
POST /api/v1/salary-calculations/batch
Authorization: Bearer {token}
Content-Type: application/json

{
    "employee_ids": [1, 2, 3, 5, 8],
    "period": "2024-06"
}
```

#### 5.3.3 获取薪资计算列表

```http
GET /api/v1/salary-calculations
Authorization: Bearer {token}
```

参数：
- `period` - 可选，期间筛选
- `status` - 可选，状态筛选
- `department_id` - 可选，部门筛选

### 5.4 工资单管理

#### 5.4.1 生成工资单

```http
POST /api/v1/payslips/salary-calculations/{salary_calculation_id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "format": "pdf",
    "include_details": true
}
```

#### 5.4.2 发送工资单

```http
POST /api/v1/payslips/{payslip_id}/send
Authorization: Bearer {token}
Content-Type: application/json

{
    "send_email": true,
    "email_message": "请查收您的24年6月工资单"
}
```

## 6. 系统管理API

### 6.1 用户管理

#### 6.1.1 获取用户列表

```http
GET /api/v1/users
Authorization: Bearer {token}
```

#### 6.1.2 创建用户

```http
POST /api/v1/users
Authorization: Bearer {token}
Content-Type: application/json

{
    "username": "teacher001",
    "email": "<EMAIL>",
    "password": "securepassword",
    "real_name": "张老师",
    "role_ids": [2],
    "phone": "13800138000"
}
```

### 6.2 角色权限

#### 6.2.1 获取角色列表

```http
GET /api/v1/roles
Authorization: Bearer {token}
```

#### 6.2.2 获取权限列表

```http
GET /api/v1/permissions
Authorization: Bearer {token}
```

参数：
- `module` - 可选，模块筛选

## 7. 错误处理

### 7.1 错误响应格式

API错误遵循JSON:API规范，使用一致的格式返回错误信息：

```json
{
    "error": {
        "code": "resource_not_found",
        "message": "请求的资源不存在",
        "status": 404,
        "details": {
            "resource": "exam",
            "id": 123
        }
    }
}
```

### 7.2 常见状态码

- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 认证失败
- `403 Forbidden` - 无权访问
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 数据验证失败
- `429 Too Many Requests` - 请求频率超限
- `500 Internal Server Error` - 服务器内部错误

## 8. 访问限制

所有API端点都配置了请求频率限制，防止过度使用和浪费资源：

```php
// 正常请求限制1分钟60次
Route::middleware(['throttle:api'])->group(function () {
    // API路由
});

// 登录/注册请求限制1分钟5次
Route::middleware(['throttle:5,1'])->group(function () {
    Route::post('/api/v1/auth/login', [AuthController::class, 'login']);
});
```

当超出限制时，返回429状态码和对应的错误信息。

## 9. 订阅模块API

### 9.1 获取当前订阅状态

```http
GET /api/v1/subscription/status
Authorization: Bearer {token}
```

响应：

```json
{
    "data": {
        "plan": "成绩分析版",
        "status": "active",
        "trial_ends_at": null,
        "renews_at": "2024-07-30T23:59:59Z",
        "available_modules": ["core", "score"],
        "on_grace_period": false,
        "canceled": false
    }
}
```

### 9.2 获取可用订阅计划

```http
GET /api/v1/subscription/plans
Authorization: Bearer {token}
```

响应：

```json
{
    "data": [
        {
            "id": "price_basic",
            "name": "基础版",
            "description": "基本用户管理功能",
            "modules": ["core"],
            "stripe_id": "price_1MzKu2GxcPPjuRfH7KuFO34A"
        },
        {
            "id": "price_score",
            "name": "成绩分析版",
            "description": "包含完整的成绩分析系统",
            "modules": ["core", "score"],
            "stripe_id": "price_1MzKTpGxcPPjuRfHrWNDKlbE"
        },
        {
            "id": "price_salary",
            "name": "工资管理版",
            "description": "包含完整的工资查询统计系统",
            "modules": ["core", "salary"],
            "stripe_id": "price_1MzKX4GxcPPjuRfHGvcKnhPa"
        },
        {
            "id": "price_complete",
            "name": "完整版",
            "description": "包含所有系统功能",
            "modules": ["core", "score", "salary"],
            "stripe_id": "price_1MzKYNGxcPPjuRfHsL2Xj6cS"
        }
    ]
}
```

### 9.3 升级订阅计划

```http
POST /api/v1/subscription/checkout
Authorization: Bearer {token}
Content-Type: application/json

{
    "plan": "price_complete",
    "return_url": "https://example.com/subscription/success"
}
```

响应：

```json
{
    "data": {
        "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1vUhY1K2MQCrPJZGWQW9DLvaMf8jPjOYz3bVgEwzaZUuIXWsJnzAiIDUV#fidkdWxOYHwnPyd1blpxYHZxWjA0TjE0PW1PTVdqSzJfXUA8Szd1Yn1JQDBVTEtpcmRpQDZnPWg0bG1qVExgYG1CSDc9NTF9SE9qT2NrMzU9MWtdSWBPNTVhPVBOd2g1T31JNTVgUycpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl"
    }
}
```

## 10. 移动端应用支持

API设计上特别考虑了移动应用的使用场景和需求：

1. **轻量级响应**：移动端可使用参数 `fields` 指定返回字段，减少数据传输量。

2. **移动通知**：支持通过 Firebase Cloud Messaging (FCM) 发送通知。

   ```http
   POST /api/v1/devices/register
   Authorization: Bearer {token}
   Content-Type: application/json

   {
       "device_token": "fcm-token-xxx",
       "device_type": "android",
       "app_version": "1.2.3"
   }
   ```

3. **离线支持**：提供特定端点用于数据同步和离线状态管理。

   ```http
   POST /api/v1/sync
   Authorization: Bearer {token}
   Content-Type: application/json

   {
       "last_sync": "2024-05-15T08:30:45Z",
       "pending_changes": [...],
       "device_id": "unique-device-id"
   }
   ```

## 11. API版本管理

系统采用URL前缀的版本管理方式：

- `/api/v1/...` - API第一版
- `/api/v2/...` - API第二版（当需要破坏性变更时引入）

旧版API将在新版发布后至少维护6个月的兼容期，维护期可能会根据需求调整。