# 模块集成示例代码

## 1. 概述

本文档提供了成绩分析系统和工资查询统计系统两个核心模块之间的集成示例代码，展示如何在保持模块独立的同时实现数据交互和功能协同。本文档专为AI开发者设计，提供完整的代码示例和集成要点。

## 2. 模块集成原则

在实现模块集成时，我们遵循以下原则：

1. **松耦合原则**：模块间通过定义清晰的接口进行交互，减少直接依赖
2. **单向依赖**：避免循环依赖，确保依赖关系清晰
3. **封装变化**：通过服务层封装模块内部变化，降低对其他模块的影响
4. **完整性原则**：确保每个模块在独立存在时仍能正常运行

## 3. 集成架构

我们采用服务层模式来实现模块间的集成：

```mermaid
graph TD
    A[控制器层] --> B[服务层]
    B --> C[模型层]
    
    subgraph 成绩分析系统
        D[ScoreController] --> E[ScoreService]
        E --> F[Score模型/Repository]
    end
    
    subgraph 工资管理系统
        G[SalaryController] --> H[SalaryService]
        H --> I[Salary模型/Repository]
    end
    
    E -.-> J[ModuleIntegrationService]
    H -.-> J
    J --> E
    J --> H
```

集成服务(`ModuleIntegrationService`)作为两个模块间的桥梁，提供标准化的数据交换接口。

## 4. 集成服务实现

### 4.1 模块集成服务接口

```php
<?php

namespace App\Services\Integration;

interface ModuleIntegrationInterface
{
    /**
     * 获取教师绩效数据（成绩系统 -> 工资系统）
     * 
     * @param int $teacherId 教师ID
     * @param string $period 时间段 (如 '2024-06' 表示2024年6月)
     * @return array 绩效数据
     */
    public function getTeacherPerformanceData(int $teacherId, string $period): array;
    
    /**
     * 获取班级成绩统计数据（成绩系统 -> 工资系统）
     * 
     * @param int $classId 班级ID
     * @param int $examId 考试ID
     * @return array 班级成绩统计数据
     */
    public function getClassScoreStatistics(int $classId, int $examId): array;
    
    /**
     * 获取教师基本信息（工资系统 -> 成绩系统）
     * 
     * @param int $teacherId 教师ID
     * @return array|null 教师信息
     */
    public function getTeacherInfo(int $teacherId): ?array;
    
    /**
     * 检查模块是否可用
     * 
     * @param string $moduleName 模块名称 ('score' 或 'salary')
     * @return bool 模块是否可用
     */
    public function isModuleAvailable(string $moduleName): bool;
}
```

### 4.2 模块集成服务实现

```php
<?php

namespace App\Services\Integration;

use App\Services\Score\ScoreAnalyticsService;
use App\Services\Salary\TeacherPerformanceService;
use App\Services\Salary\TeacherInfoService;

class ModuleIntegrationService implements ModuleIntegrationInterface
{
    protected $scoreAnalyticsService;
    protected $teacherPerformanceService;
    protected $teacherInfoService;
    
    public function __construct(
        ScoreAnalyticsService $scoreAnalyticsService,
        TeacherPerformanceService $teacherPerformanceService,
        TeacherInfoService $teacherInfoService
    ) {
        $this->scoreAnalyticsService = $scoreAnalyticsService;
        $this->teacherPerformanceService = $teacherPerformanceService;
        $this->teacherInfoService = $teacherInfoService;
    }
    
    public function getTeacherPerformanceData(int $teacherId, string $period): array
    {
        // 检查成绩模块是否可用
        if (!$this->isModuleAvailable('score')) {
            return [
                'available' => false,
                'message' => 'Score module is not available',
                'data' => []
            ];
        }
        
        // 从成绩系统获取教师班级的学生成绩数据
        $classesData = $this->scoreAnalyticsService->getTeacherClassesPerformance($teacherId, $period);
        
        // 处理数据格式以适应工资系统需求
        $performanceData = [
            'available' => true,
            'classes' => [],
            'performance_indicators' => []
        ];
        
        foreach ($classesData as $classData) {
            $performanceData['classes'][] = [
                'class_id' => $classData['class_id'],
                'class_name' => $classData['class_name'],
                'avg_score' => $classData['avg_score'],
                'pass_rate' => $classData['pass_rate'],
                'excellent_rate' => $classData['excellent_rate'],
                'improvement' => $classData['improvement']
            ];
            
            // 计算综合绩效指标
            $performanceScore = $this->calculatePerformanceScore($classData);
            $performanceData['performance_indicators'][] = [
                'class_id' => $classData['class_id'],
                'performance_score' => $performanceScore,
                'factors' => [
                    'avg_score_factor' => $classData['avg_score'] / 100 * 0.4,
                    'pass_rate_factor' => $classData['pass_rate'] * 0.3,
                    'excellent_rate_factor' => $classData['excellent_rate'] * 0.2,
                    'improvement_factor' => $classData['improvement'] * 0.1
                ]
            ];
        }
        
        return $performanceData;
    }
    
    protected function calculatePerformanceScore(array $classData): float
    {
        // 综合考虑平均分、及格率、优秀率和进步率等因素计算绩效分数
        $score = ($classData['avg_score'] / 100 * 0.4) + 
                 ($classData['pass_rate'] * 0.3) + 
                 ($classData['excellent_rate'] * 0.2) + 
                 ($classData['improvement'] * 0.1);
        
        return round($score * 100, 2); // 转换为百分制
    }
    
    public function getClassScoreStatistics(int $classId, int $examId): array
    {
        if (!$this->isModuleAvailable('score')) {
            return [
                'available' => false,
                'message' => 'Score module is not available',
                'data' => []
            ];
        }
        
        return $this->scoreAnalyticsService->getClassStatistics($classId, $examId);
    }
    
    public function getTeacherInfo(int $teacherId): ?array
    {
        if (!$this->isModuleAvailable('salary')) {
            return null;
        }
        
        return $this->teacherInfoService->getTeacherBasicInfo($teacherId);
    }
    
    public function isModuleAvailable(string $moduleName): bool
    {
        // 检查当前租户是否订阅了指定模块
        if (!function_exists('tenant') || !tenant()) {
            return false;
        }
        
        return tenant()->hasModuleAccess($moduleName);
    }
}
```

## 5. 成绩系统服务示例

```php
<?php

namespace App\Services\Score;

use App\Repositories\Score\ScoreRepository;
use App\Repositories\Score\ExamRepository;
use App\Repositories\Score\ClassRepository;

class ScoreAnalyticsService
{
    protected $scoreRepository;
    protected $examRepository;
    protected $classRepository;
    
    public function __construct(
        ScoreRepository $scoreRepository,
        ExamRepository $examRepository,
        ClassRepository $classRepository
    ) {
        $this->scoreRepository = $scoreRepository;
        $this->examRepository = $examRepository;
        $this->classRepository = $classRepository;
    }
    
    /**
     * 获取教师班级的成绩表现数据
     */
    public function getTeacherClassesPerformance(int $teacherId, string $period): array
    {
        // 解析时间段
        list($year, $month) = explode('-', $period);
        
        // 获取该教师教授的班级
        $classes = $this->classRepository->getClassesByTeacherId($teacherId);
        $result = [];
        
        foreach ($classes as $class) {
            // 获取指定时间段内的考试
            $exams = $this->examRepository->getExamsByPeriod($year, $month, $class->id);
            
            // 如果没有考试，跳过该班级
            if ($exams->isEmpty()) {
                continue;
            }
            
            // 计算当前成绩数据
            $currentExam = $exams->last();
            $currentStats = $this->scoreRepository->getClassStatistics($class->id, $currentExam->id);
            
            // 计算上一次考试的成绩数据（用于计算进步率）
            $previousStats = null;
            if ($exams->count() > 1) {
                $previousExam = $exams[$exams->count() - 2];
                $previousStats = $this->scoreRepository->getClassStatistics($class->id, $previousExam->id);
            }
            
            // 计算进步率
            $improvement = 0;
            if ($previousStats) {
                $improvement = ($currentStats['avg_score'] - $previousStats['avg_score']) / $previousStats['avg_score'];
            }
            
            $result[] = [
                'class_id' => $class->id,
                'class_name' => $class->name,
                'exam_id' => $currentExam->id,
                'exam_name' => $currentExam->name,
                'avg_score' => $currentStats['avg_score'],
                'pass_rate' => $currentStats['pass_rate'],
                'excellent_rate' => $currentStats['excellent_rate'],
                'improvement' => $improvement
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取班级统计数据
     */
    public function getClassStatistics(int $classId, int $examId): array
    {
        $stats = $this->scoreRepository->getClassStatistics($classId, $examId);
        
        return [
            'available' => true,
            'class_id' => $classId,
            'exam_id' => $examId,
            'data' => $stats
        ];
    }
}
```

## 6. 工资系统服务示例

```php
<?php

namespace App\Services\Salary;

use App\Repositories\Salary\TeacherRepository;
use App\Repositories\Salary\SalaryComponentRepository;
use App\Services\Integration\ModuleIntegrationInterface;

class TeacherPerformanceService
{
    protected $teacherRepository;
    protected $salaryComponentRepository;
    protected $integrationService;
    
    public function __construct(
        TeacherRepository $teacherRepository,
        SalaryComponentRepository $salaryComponentRepository,
        ModuleIntegrationInterface $integrationService
    ) {
        $this->teacherRepository = $teacherRepository;
        $this->salaryComponentRepository = $salaryComponentRepository;
        $this->integrationService = $integrationService;
    }
    
    /**
     * 计算教师绩效工资
     */
    public function calculatePerformanceBonus(int $teacherId, string $period): array
    {
        // 获取教师基本信息
        $teacher = $this->teacherRepository->find($teacherId);
        
        if (!$teacher) {
            return [
                'success' => false,
                'message' => 'Teacher not found'
            ];
        }
        
        // 尝试从成绩系统获取绩效数据
        $performanceData = $this->integrationService->getTeacherPerformanceData($teacherId, $period);
        
        // 如果成绩模块不可用或无数据，使用基本绩效计算
        if (!isset($performanceData['available']) || !$performanceData['available']) {
            // 使用基本绩效计算规则
            return $this->calculateBasicPerformanceBonus($teacher, $period);
        }
        
        // 使用成绩数据计算绩效奖金
        $bonusAmount = 0;
        $details = [];
        
        // 获取绩效基数
        $baseBonus = $this->salaryComponentRepository->getComponentValue('performance_base', $teacherId);
        
        foreach ($performanceData['performance_indicators'] as $indicator) {
            // 根据绩效指标计算奖金
            $classBonus = $baseBonus * ($indicator['performance_score'] / 100);
            
            $details[] = [
                'class_id' => $indicator['class_id'],
                'performance_score' => $indicator['performance_score'],
                'base_bonus' => $baseBonus,
                'class_bonus' => $classBonus,
                'factors' => $indicator['factors']
            ];
            
            $bonusAmount += $classBonus;
        }
        
        return [
            'success' => true,
            'teacher_id' => $teacherId,
            'period' => $period,
            'total_bonus' => $bonusAmount,
            'details' => $details,
            'data_source' => 'score_system'
        ];
    }
    
    /**
     * 基本绩效计算（当成绩模块不可用时）
     */
    protected function calculateBasicPerformanceBonus($teacher, string $period): array
    {
        // 获取绩效基数
        $baseBonus = $this->salaryComponentRepository->getComponentValue('performance_base', $teacher->id);
        
        // 基本绩效系数（无成绩数据时默认为0.8）
        $performanceFactor = 0.8;
        
        // 计算绩效奖金
        $bonusAmount = $baseBonus * $performanceFactor;
        
        return [
            'success' => true,
            'teacher_id' => $teacher->id,
            'period' => $period,
            'total_bonus' => $bonusAmount,
            'details' => [
                [
                    'performance_factor' => $performanceFactor,
                    'base_bonus' => $baseBonus,
                    'bonus' => $bonusAmount
                ]
            ],
            'data_source' => 'basic_calculation'
        ];
    }
}
```

## 7. 前端集成示例

### 7.1 集成组件示例

```vue
<template>
  <div class="teacher-performance-dashboard">
    <h2>教师绩效仪表板</h2>
    
    <!-- 模块可用性提示 -->
    <div v-if="!scoreModuleAvailable" class="module-unavailable-notice">
      <AlertCircle class="icon" />
      <div class="notice-content">
        <h3>成绩分析模块未启用</h3>
        <p>您当前的订阅不包含成绩分析模块。启用此模块可获得更准确的绩效分析和数据可视化。</p>
        <Button @click="upgradeSubscription">升级订阅</Button>
      </div>
    </div>
    
    <div class="performance-metrics">
      <!-- 基本绩效信息 -->
      <div class="metric-card">
        <h3>基本绩效信息</h3>
        <div class="metric-value">{{ performanceData.total_bonus }}</div>
        <div class="metric-label">总绩效奖金</div>
      </div>
      
      <!-- 班级绩效信息 - 仅当成绩模块可用时显示 -->
      <template v-if="scoreModuleAvailable && performanceData.data_source === 'score_system'">
        <div v-for="detail in performanceData.details" :key="detail.class_id" class="metric-card">
          <h3>{{ getClassName(detail.class_id) }}</h3>
          <div class="metric-value">{{ detail.performance_score }}%</div>
          <div class="metric-label">绩效得分</div>
          <div class="bonus-amount">奖金: {{ detail.class_bonus }}</div>
          
          <!-- 绩效得分因素明细 -->
          <div class="factors-breakdown">
            <div class="factor">
              <div class="factor-label">平均分因子</div>
              <div class="factor-value">{{ (detail.factors.avg_score_factor * 100).toFixed(1) }}%</div>
            </div>
            <div class="factor">
              <div class="factor-label">及格率因子</div>
              <div class="factor-value">{{ (detail.factors.pass_rate_factor * 100).toFixed(1) }}%</div>
            </div>
            <div class="factor">
              <div class="factor-label">优秀率因子</div>
              <div class="factor-value">{{ (detail.factors.excellent_rate_factor * 100).toFixed(1) }}%</div>
            </div>
            <div class="factor">
              <div class="factor-label">进步率因子</div>
              <div class="factor-value">{{ (detail.factors.improvement_factor * 100).toFixed(1) }}%</div>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 成绩数据明细 - 仅当成绩模块可用时显示 -->
    <div v-if="scoreModuleAvailable && classesData.length > 0" class="score-details">
      <h3>班级成绩明细</h3>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>班级</TableHead>
            <TableHead>考试</TableHead>
            <TableHead>平均分</TableHead>
            <TableHead>及格率</TableHead>
            <TableHead>优秀率</TableHead>
            <TableHead>进步率</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="cls in classesData" :key="cls.class_id">
            <TableCell>{{ cls.class_name }}</TableCell>
            <TableCell>{{ cls.exam_name }}</TableCell>
            <TableCell>{{ cls.avg_score.toFixed(1) }}</TableCell>
            <TableCell>{{ (cls.pass_rate * 100).toFixed(1) }}%</TableCell>
            <TableCell>{{ (cls.excellent_rate * 100).toFixed(1) }}%</TableCell>
            <TableCell>
              <div :class="{'improvement-positive': cls.improvement > 0, 'improvement-negative': cls.improvement < 0}">
                {{ (cls.improvement * 100).toFixed(1) }}%
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import { AlertCircle } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useModuleStore } from '@/stores/module'
import { useTeacherStore } from '@/stores/teacher'
import { useClassStore } from '@/stores/class'

const moduleStore = useModuleStore()
const teacherStore = useTeacherStore()
const classStore = useClassStore()

// 当前教师ID和期间
const teacherId = ref(1) // 实际应用中应从路由或prop获取
const currentPeriod = ref('2024-06')

// 数据状态
const performanceData = ref({
  total_bonus: 0,
  details: [],
  data_source: ''
})
const classesData = ref([])

// 计算属性
const scoreModuleAvailable = computed(() => moduleStore.isModuleAvailable('score'))

// 方法
const fetchData = async () => {
  // 获取绩效数据
  const performance = await teacherStore.getPerformanceData(teacherId.value, currentPeriod.value)
  performanceData.value = performance
  
  // 如果成绩模块可用，获取班级数据
  if (scoreModuleAvailable.value) {
    const classes = await teacherStore.getTeacherClassesData(teacherId.value, currentPeriod.value)
    classesData.value = classes
  }
}

const getClassName = (classId) => {
  const cls = classesData.value.find(c => c.class_id === classId)
  return cls ? cls.class_name : `班级 ${classId}`
}

const upgradeSubscription = () => {
  router.visit(route('subscription.plans'))
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.teacher-performance-dashboard {
  padding: 1.5rem;
}

.module-unavailable-notice {
  display: flex;
  padding: 1rem;
  background-color: #fff9db;
  border: 1px solid #ffd43b;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.module-unavailable-notice .icon {
  color: #f59f00;
  margin-right: 1rem;
}

.notice-content h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #f08c00;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.metric-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #495057;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.bonus-amount {
  margin-top: 0.5rem;
  font-weight: bold;
  color: #2b8a3e;
}

.factors-breakdown {
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e9ecef;
}

.factor {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.factor-label {
  font-size: 0.75rem;
  color: #6c757d;
}

.factor-value {
  font-size: 0.75rem;
  font-weight: 500;
}

.improvement-positive {
  color: #2b8a3e;
}

.improvement-negative {
  color: #c92a2a;
}
</style>
```

### 7.2 Pinia 状态管理集成

```javascript
// stores/module.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'

export const useModuleStore = defineStore('module', () => {
  // 状态
  const availableModules = ref([])
  const loadingModules = ref(true)
  
  // 计算属性
  const isModuleAvailable = computed(() => (moduleName) => {
    return availableModules.value.includes(moduleName)
  })
  
  // 动作
  const fetchAvailableModules = async () => {
    loadingModules.value = true
    try {
      const response = await api.get('/api/tenant/modules')
      availableModules.value = response.data.modules || []
    } catch (error) {
      console.error('Error fetching available modules:', error)
      availableModules.value = []
    } finally {
      loadingModules.value = false
    }
  }
  
  return {
    availableModules,
    loadingModules,
    isModuleAvailable,
    fetchAvailableModules
  }
})

// stores/teacher.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/api'

export const useTeacherStore = defineStore('teacher', () => {
  // 状态
  const loading = ref(false)
  const error = ref(null)
  
  // 动作
  const getPerformanceData = async (teacherId, period) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get(`/api/teachers/${teacherId}/performance`, {
        params: { period }
      })
      return response.data
    } catch (err) {
      error.value = err.message || 'Failed to fetch performance data'
      return {
        total_bonus: 0,
        details: [],
        data_source: 'error'
      }
    } finally {
      loading.value = false
    }
  }
  
  const getTeacherClassesData = async (teacherId, period) => {
    try {
      const response = await api.get(`/api/teachers/${teacherId}/classes/performance`, {
        params: { period }
      })
      return response.data || []
    } catch (err) {
      console.error('Failed to fetch teacher classes data:', err)
      return []
    }
  }
  
  return {
    loading,
    error,
    getPerformanceData,
    getTeacherClassesData
  }
})
```

## 8. 控制器集成示例

```php
<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\Salary\TeacherPerformanceService;
use App\Services\Integration\ModuleIntegrationInterface;
use Illuminate\Http\Request;

class TeacherPerformanceController extends Controller
{
    protected $performanceService;
    protected $integrationService;
    
    public function __construct(
        TeacherPerformanceService $performanceService,
        ModuleIntegrationInterface $integrationService
    ) {
        $this->performanceService = $performanceService;
        $this->integrationService = $integrationService;
    }
    
    /**
     * 获取教师绩效数据
     */
    public function getPerformance(Request $request, $teacherId)
    {
        $period = $request->input('period', date('Y-m'));
        
        // 计算绩效奖金
        $performanceData = $this->performanceService->calculatePerformanceBonus($teacherId, $period);
        
        return response()->json($performanceData);
    }
    
    /**
     * 获取教师班级绩效数据
     */
    public function getClassPerformance(Request $request, $teacherId)
    {
        $period = $request->input('period', date('Y-m'));
        
        // 检查成绩模块是否可用
        if (!$this->integrationService->isModuleAvailable('score')) {
            return response()->json([
                'message' => 'Score module is not available',
                'data' => []
            ], 403);
        }
        
        // 从集成服务获取教师班级的成绩数据
        $performanceData = $this->integrationService->getTeacherPerformanceData($teacherId, $period);
        
        return response()->json($performanceData['classes'] ?? []);
    }
}
```

## 9. 路由定义

```php
<?php

// 工资系统API路由
Route::middleware(['auth', 'verified', 'module:salary'])
    ->prefix('api/teachers')
    ->group(function () {
        Route::get('{teacherId}/performance', [TeacherPerformanceController::class, 'getPerformance']);
        
        // 集成路由 - 需要检查成绩模块是否可用
        Route::get('{teacherId}/classes/performance', [TeacherPerformanceController::class, 'getClassPerformance']);
    });

// 成绩系统API路由
Route::middleware(['auth', 'verified', 'module:score'])
    ->prefix('api/scores')
    ->group(function () {
        Route::get('classes/{classId}/exams/{examId}/statistics', [ScoreController::class, 'getClassStatistics']);
    });
```

## 10. 模块注册和服务提供者

```php
<?php

namespace App\Providers;

use App\Services\Integration\ModuleIntegrationInterface;
use App\Services\Integration\ModuleIntegrationService;
use Illuminate\Support\ServiceProvider;

class IntegrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ModuleIntegrationInterface::class, function ($app) {
            return new ModuleIntegrationService(
                $app->make(\App\Services\Score\ScoreAnalyticsService::class),
                $app->make(\App\Services\Salary\TeacherPerformanceService::class),
                $app->make(\App\Services\Salary\TeacherInfoService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
```

## 11. 集成测试示例

```php
<?php

namespace Tests\Feature\Integration;

use App\Models\Teacher;
use App\Models\Tenant;
use App\Services\Integration\ModuleIntegrationInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Stancl\Tenancy\Testing\TenantTestCase;
use Tests\TestCase;

class ModuleIntegrationTest extends TenantTestCase
{
    use RefreshDatabase;
    
    protected $teacher;
    protected $tenant;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建租户（完整版订阅 - 包含两个模块）
        $this->tenant = Tenant::create([
            'id' => 'test',
            // 其他租户属性...
        ]);
        
        // 模拟订阅
        $this->mockSubscription($this->tenant, 'price_complete');
        
        // 初始化租户
        $this->tenant->domains()->create(['domain' => 'test.localhost']);
        tenancy()->initialize($this->tenant);
        
        // 创建测试教师
        $this->teacher = Teacher::factory()->create();
    }
    
    public function test_module_availability_check()
    {
        $integrationService = app(ModuleIntegrationInterface::class);
        
        // 完整版订阅应该可以访问两个模块
        $this->assertTrue($integrationService->isModuleAvailable('score'));
        $this->assertTrue($integrationService->isModuleAvailable('salary'));
        
        // 更改为仅工资模块订阅
        $this->mockSubscription($this->tenant, 'price_salary');
        
        // 应该只能访问工资模块
        $this->assertFalse($integrationService->isModuleAvailable('score'));
        $this->assertTrue($integrationService->isModuleAvailable('salary'));
    }
    
    public function test_performance_data_integration()
    {
        $integrationService = app(ModuleIntegrationInterface::class);
        $period = '2024-06';
        
        // 创建测试数据
        // ...
        
        // 获取绩效数据
        $performanceData = $integrationService->getTeacherPerformanceData($this->teacher->id, $period);
        
        // 验证数据结构
        $this->assertTrue($performanceData['available']);
        $this->assertIsArray($performanceData['classes']);
        $this->assertIsArray($performanceData['performance_indicators']);
        
        // 更改为仅工资模块订阅
        $this->mockSubscription($this->tenant, 'price_salary');
        
        // 当成绩模块不可用时，应返回不可用状态
        $performanceData = $integrationService->getTeacherPerformanceData($this->teacher->id, $period);
        $this->assertFalse($performanceData['available']);
    }
    
    /**
     * 模拟租户订阅
     */
    private function mockSubscription($tenant, $priceId)
    {
        // 先删除现有订阅
        $tenant->subscriptions()->delete();
        
        // 创建订阅记录
        $subscription = $tenant->subscriptions()->create([
            'name' => 'default',
            'stripe_id' => 'sub_' . uniqid(),
            'stripe_status' => 'active',
            'stripe_price' => $priceId,
            'quantity' => 1,
            'trial_ends_at' => null,
            'ends_at' => null,
        ]);
        
        // 创建订阅项目
        $subscription->items()->create([
            'stripe_id' => 'si_' . uniqid(),
            'stripe_product' => 'prod_' . uniqid(),
            'stripe_price' => $priceId,
            'quantity' => 1,
        ]);
        
        // 刷新租户实例
        $tenant->refresh();
    }
}
```

## 12. 最佳实践建议

在实现模块集成时，应遵循以下最佳实践：

1. **优雅降级**：当依赖模块不可用时，应提供基础功能
2. **透明访问检查**：对用户明确说明需要哪些模块订阅
3. **性能考虑**：缓存模块可用性状态，避免每次请求都检查
4. **数据一致性**：确保跨模块数据交互的一致性和完整性
5. **单一职责**：每个服务应专注于自己的核心功能
6. **接口稳定性**：模块间接口应保持稳定，避免频繁变更
7. **明确错误处理**：当模块不可用时提供清晰的错误消息
8. **全面测试**：测试不同订阅组合下的功能可用性

通过这些集成示例，可以实现成绩分析系统和工资管理系统之间的无缝集成，在保持两个模块相对独立的同时提供更丰富的功能和更好的用户体验。