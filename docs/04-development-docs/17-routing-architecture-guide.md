# 路由架构指南

## 1. 概述

教学事务管理系统基于 scoreDB25/saas 框架，采用模块化的路由架构，结合多租户识别机制，实现了基于租户和订阅的路由控制。系统使用多域名架构，通过不同域名实现功能隔离和安全控制。本文档详细介绍系统的路由架构设计、租户识别机制及模块化路由组织方式。

## 2. 路由层次结构

系统路由架构分为三个主要层次：

```mermaid
flowchart TD
    A[请求] --> B{路由类型}
    B -->|中央平台路由| C[Central Routes]
    B -->|租户Web路由| D[Tenant Web Routes]
    B -->|租户API路由| E[Tenant API Routes]
    D --> F{模块检查}
    E --> F
    F -->|无订阅| G[订阅页面]
    F -->|已订阅| H[请求处理]
```

### 2.1 中央平台路由

中央平台路由处理非租户特定的功能，如租户注册、登录和系统管理等。这些路由定义在`routes/web.php`中。

### 2.2 租户Web路由

租户Web路由处理特定租户的Web请求，定义在`routes/tenant.php`文件中。这些路由通过域名识别机制自动确定当前租户。

### 2.3 租户API路由

租户API路由处理特定租户的API请求，可通过以下方式进行租户识别：

1. 域名识别（例如：`tenant1.example.com/api/scores`）
2. 请求头识别（通过`X-Tenant-ID`头）
3. 请求参数识别（通过`tenant`查询参数）

## 3. 模块化路由组织

### 3.1 模块路由目录结构

系统在`routes/modules/`目录下按功能模块组织路由文件：

```
routes/
  ├── web.php           # 中央平台Web路由
  ├── api.php           # 中央平台API路由
  ├── tenant.php        # 租户通用Web路由
  ├── tenant/
  │   └── api.php       # 租户通用API路由
  └── modules/
      ├── score.php     # 成绩分析模块路由
      └── salary.php    # 工资管理模块路由
```

每个模块文件同时包含该模块的Web路由和API路由定义。

### 3.2 模块路由示例

以下是一个模块路由文件的示例结构：

```php
<?php

declare(strict_types=1);

use App\Http\Controllers\ExamController;
use Illuminate\Support\Facades\Route;

// 定义模块级别的Web路由
Route::middleware(['web', 'auth'])->group(function () {
    Route::get('/', function() {
        return inertia('tenant/score/Index');
    })->name('index');

    Route::get('/exams', function() {
        return inertia('tenant/exams/Index');
    })->name('exams.index');
});

// 定义模块的API路由
Route::name('api.')
    ->prefix('api')
    ->middleware(['api'])
    ->group(function () {
        Route::apiResource('exams', ExamController::class);
    });
```

## 4. 租户识别中间件

### 4.1 Web请求租户识别

系统使用`InitializeTenancyByDomainOrSubdomain`中间件识别Web请求的租户，支持域名和子域名两种模式：

```php
Route::middleware([
    'web',
    \Stancl\Tenancy\Middleware\InitializeTenancyByDomainOrSubdomain::class,
    \Stancl\Tenancy\Middleware\PreventAccessFromUnwantedDomains::class,
])->group(function () {
    // 租户路由定义
});
```

### 4.2 API请求租户识别

系统使用自定义的`InitializeTenancyForApi`中间件识别API请求的租户，支持多种识别方式：

```php
class InitializeTenancyForApi implements IdentifiesTenants
{
    public function identify(Request $request): ?Tenant
    {
        // 通过域名识别
        try {
            $domain = $request->getHost();
            if ($domain && $domain !== config('app.url')) {
                if ($tenant = $this->tenancy->findByDomain($domain)) {
                    return $tenant;
                }
            }
        } catch (\Exception $e) {
            // 继续尝试其他方法
        }

        // 通过X-Tenant-ID头识别
        $tenantId = $request->header('X-Tenant-ID');
        if ($tenantId) {
            try {
                if ($tenant = $this->tenancy->find($tenantId)) {
                    return $tenant;
                }
            } catch (\Exception $e) {
                // 继续尝试其他方法
            }
        }

        // 通过URL查询参数识别
        $tenantId = $request->query('tenant');
        if ($tenantId) {
            try {
                return $this->tenancy->find($tenantId);
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }
}
```

## 5. 模块访问控制

### 5.1 模块访问中间件

系统使用`EnsureModuleAccess`中间件检查租户是否有权访问特定模块：

```php
class EnsureModuleAccess
{
    public function handle(Request $request, Closure $next, string $module): Response
    {
        // 核心模块对所有已认证用户开放
        if ($module === 'core') {
            return $next($request);
        }

        // 获取租户订阅
        $tenant = tenant();
        $subscription = $tenant->subscription('default');

        // 试用期内可访问所有模块
        if ($tenant->onTrial()) {
            return $next($request);
        }

        // 检查订阅状态和模块权限
        if (!$subscription || !$tenant->subscribed('default')) {
            return redirect()->route('tenant.subscription.required')
                ->with('module', $module);
        }

        // 检查当前订阅计划是否包含请求的模块
        $plan = $subscription->stripe_price;
        $allowedModules = config("saas.plan_modules.{$plan}.modules", []);

        if (!in_array($module, $allowedModules)) {
            return redirect()->route('tenant.subscription.upgrade')
                ->with('module', $module);
        }

        return $next($request);
    }
}
```

### 5.2 路由与模块访问集成

模块访问控制通过中间件与路由注册集成：

```php
// 在ModuleServiceProvider中
protected function registerModuleRoutes(): void
{
    // 自动加载模块路由文件
    $modulesPath = base_path('routes/modules');

    if (is_dir($modulesPath)) {
        $files = scandir($modulesPath);

        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                $module = pathinfo($file, PATHINFO_FILENAME);

                // Web路由 - 使用tenant中间件组和模块访问检查
                Route::middleware(['tenant', 'ensure.module.access:' . $module])
                    ->prefix($module)
                    ->name($module . '.')
                    ->group($modulesPath . '/' . $file);

                // API路由 - 使用tenant.api中间件组和模块访问检查
                Route::middleware(['tenant.api', 'ensure.module.access:' . $module])
                    ->prefix('api/' . $module)
                    ->name('api.' . $module . '.')
                    ->group($modulesPath . '/' . $file);
            }
        }
    }
}
```

## 6. 最佳实践

### 6.1 新模块路由添加流程

添加新模块的推荐步骤：

1. 在`routes/modules/`目录下创建新的模块路由文件（例如：`courses.php`）
2. 在该文件中定义模块的Web路由和API路由
3. 确保在`config/saas.php`中正确配置该模块的订阅关系
4. 重启应用，系统将自动加载并注册新模块的路由

### 6.2 路由命名约定

为保持一致性，应遵循以下命名约定：

- 模块Web路由：`{module}.{resource}.{action}`（例如：`score.exams.index`）
- 模块API路由：`api.{module}.{resource}.{action}`（例如：`api.score.exams.store`）

### 6.3 租户API访问示例

API访问示例：

```javascript
// 1. 通过域名访问（推荐）
axios.get('https://test.scoredb25.ddev.site/api/score/exams')

// 2. 通过请求头指定租户
axios.get('https://scoredb25.ddev.site/api/score/exams', {
  headers: {
    'X-Tenant-ID': 'test'
  }
})

// 3. 通过查询参数指定租户（不推荐用于生产环境）
axios.get('https://scoredb25.ddev.site/api/score/exams?tenant=test')

// 4. 管理后台API访问
axios.get('https://hiddenadmin.scoredb25.ddev.site/api/admin/tenants')
```

## 7. 路由调试和测试

### 7.1 列出所有路由

使用以下命令列出系统中的所有路由：

```bash
php artisan route:list
```

### 7.2 调试特定模块路由

```bash
php artisan route:list --name=score
```

### 7.3 测试API路由

使用Postman或类似工具测试API路由，记得设置适当的租户识别信息（域名、请求头或查询参数）。

## 8. 总结

EduSaaS系统采用灵活的模块化路由架构，结合多租户识别机制和订阅访问控制，实现了以下功能：

1. 按功能模块组织路由，提高代码可维护性
2. 支持多种租户识别方式，适应不同接入场景
3. 基于订阅的模块访问控制，实现灵活的功能授权
4. 统一的路由命名和前缀约定，保持系统一致性

这种架构为系统提供了良好的可扩展性，可以方便地添加新的功能模块而无需修改核心框架代码。