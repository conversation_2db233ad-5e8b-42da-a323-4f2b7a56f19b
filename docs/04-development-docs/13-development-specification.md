# 开发规范指南

## 1. 概述

本文档为 EduSaaS 系统开发团队提供了统一的开发规范和最佳实践指南，确保代码质量、一致性和可维护性。特别重要的是，本文档包含了在多租户环境下开发工作的专用指南，以及 AI 开发者的注意事项。

## 2. 代码规范

### 2.1 PHP 代码规范

遵循 PSR-12 编码规范，并遵循以下附加规则：

1. **类结构**:
   ```php
   <?php
   
   declare(strict_types=1);
   
   namespace App\Domain\Module;
   
   use Other\Class;
   
   /**
    * 类注释
    */
   class ExampleClass
   {
       // 常量
       private const EXAMPLE_CONSTANT = 'value';
       
       // 属性
       private string $property;
       private OtherClass $typedProperty;
       
       // 构造函数
       public function __construct(OtherClass $dependency)
       {
           $this->typedProperty = $dependency;
       }
       
       // 公共方法
       public function publicMethod(): ReturnType
       {
           // ...
       }
       
       // 受保护方法
       protected function protectedMethod(): void
       {
           // ...
       }
       
       // 私有方法
       private function privateMethod(): string
       {
           return self::EXAMPLE_CONSTANT;
       }
   }
   ```

2. **类型声明**:
   - 所有属性必须声明类型
   - 所有方法必须声明返回类型 (包括 `void` 和 `mixed`)
   - 所有方法参数必须声明类型

3. **注释**:
   - 所有类、方法、复杂逻辑均需要添加注释
   - 方法注释必须包含参数类型和返回类型
   - 使用 PHPDoc 格式注释

4. **命名规范**:
   - 类名: 大驼峰命名法 (PascalCase), 如 `TeacherService`
   - 方法名: 小驼峰命名法 (camelCase), 如 `calculateSalary`
   - 属性名: 小驼峰命名法, 如 `teacherRepository`
   - 常量名: 全大写下划线分隔, 如 `MAX_ATTEMPTS`

5. **依赖注入**:
   - 除特殊情况外，使用构造函数注入所有依赖
   - 避免使用服务定位器 (`app()` 或 `resolve()`)
   - 永远针对接口而非具体实现类注入

### 2.2 JavaScript/TypeScript 代码规范

使用 ESLint 和 Prettier 进行代码规范化，并遵循以下规则：

1. **类型安全**:
   - 所有代码均使用 TypeScript 开发
   - 避免使用 `any` 类型，优先使用 `unknown`
   - 为函数参数和返回值提供类型注解

2. **组件规范**:
   - 使用 Vue 3 Composition API
   - 优先使用 `<script setup>` 语法
   - 组件名称使用 PascalCase
   - 自定义组件标签使用 PascalCase
   - 样式使用 scoped 或 CSS 模块

3. **命名规范**:
   - 变量使用小驼峰 (camelCase)
   - 组件使用大驼峰 (PascalCase)
   - 接口和类型定义使用大驼峰并以 `I` 或 `T` 前缀，如 `IUserData` 或 `TApiResponse`
   - 常量使用全大写下划线分隔

4. **Vue 组件结构**:
   ```vue
   <template>
     <div class="component-wrapper">
       <!-- 组件模板... -->
     </div>
   </template>
   
   <script setup lang="ts">
   // 导入
   import { ref, computed, onMounted } from 'vue'
   import type { PropType } from 'vue'
   import { useStore } from '@/stores/main'
   
   // 类型定义
   interface IUserData {
     id: number
     name: string
   }
   
   // Props
   const props = defineProps({
     user: {
       type: Object as PropType<IUserData>,
       required: true
     },
     loading: {
       type: Boolean,
       default: false
     }
   })
   
   // Emits
   const emit = defineEmits<{
     (e: 'update', value: string): void
     (e: 'delete', id: number): void
   }>()
   
   // Refs
   const count = ref<number>(0)
   
   // Computed
   const formattedName = computed(() => {
     return props.user.name.toUpperCase()
   })
   
   // 方法
   const increment = () => {
     count.value++
   }
   
   // 生命周期钩子
   onMounted(() => {
     // 初始化逻辑...
   })
   </script>
   
   <style scoped>
   .component-wrapper {
     /* 样式... */
   }
   </style>
   ```

5. **状态管理**:
   - 使用 Pinia 进行状态管理
   - Store 文件使用函数式 API
   - 为所有 store 状态定义类型接口

### 2.3 CSS/SCSS 规范

1. **命名约定**:
   - 使用 kebab-case 命名样式类 (如 `.user-profile`)
   - 组件根元素使用组件名称作为前缀 (如 `.score-card`)
   - 使用 BEM 命名法的简化版本

2. **样式组织**:
   - 优先使用 Tailwind CSS 类
   - 对于复杂样式，使用 scoped CSS 或 CSS 模块
   - 全局样式定义在单独的文件中

3. **响应式布局**:
   - 使用 Tailwind CSS 的断点系统
   - 优先使用 Flexbox 和 Grid 而非浮动或绝对定位
   - 使用相对单位 (rem/em) 而非固定单位 (px)

## 3. 架构规范

### 3.1 目录结构

```
app/
├── Console/            # 命令行命令
├── Exceptions/         # 异常处理
├── Http/
│   ├── Controllers/     # 控制器
│   │   ├── API/         # API 控制器
│   │   ├── Salary/      # 工资模块控制器
│   │   └── Score/       # 成绩模块控制器
│   ├── Middleware/      # 中间件
│   ├── Requests/        # 表单请求验证
│   └── Resources/       # API 资源
├── Models/             # Eloquent 模型
├── Providers/          # 服务提供者
├── Repositories/       # 数据存储层
│   ├── Contracts/      # 仓库接口
│   ├── Eloquent/       # Eloquent 实现
│   ├── Salary/         # 工资模块仓库
│   └── Score/          # 成绩模块仓库
├── Services/           # 业务逻辑层
│   ├── Contracts/      # 服务接口
│   ├── Integration/    # 模块集成服务
│   ├── Salary/         # 工资模块服务
│   └── Score/          # 成绩模块服务
└── Tenancy/            # 多租户相关功能
    ├── Listeners/      # 租户事件监听器
    └── Middleware/     # 租户中间件


resources/
├── js/
│   ├── components/      # Vue 组件
│   │   ├── ui/           # 基础 UI 组件
│   │   ├── layout/        # 布局组件
│   │   ├── salary/        # 工资模块组件
│   │   └── score/         # 成绩模块组件
│   ├── composables/     # Vue 组合式函数
│   ├── layouts/         # 布局定义
│   ├── pages/           # 页面组件
│   ├── router/          # Vue Router 配置
│   ├── stores/          # Pinia 状态存储
│   ├── types/           # TypeScript 类型定义
│   └── utils/           # 工具函数
├── css/              # 全局样式
└── views/            # Blade 模板
```

### 3.2 模块化

系统采用模块化设计，各模块遵循以下原则：

1. **模块独立性**:
   - 每个模块应包含自己的模型、仓库、服务和控制器
   - 模块间交互通过集成服务进行

2. **模块访问控制**:
   - 使用 `module` 中间件控制对模块的访问
   - 每个控制器类和路由组都应指定其所属模块

3. **模块配置**:
   - 在 `config/modules.php` 中定义模块及其所属订阅级别
   - 每个模块可以有自己的配置文件

### 3.3 层次结构

遵循清晰的层次分离：

1. **表现层**（Controllers/Resources）：处理请求和响应
2. **服务层**（Services）：包含核心业务逻辑
3. **仓库层**（Repositories）：负责数据访问
4. **模型层**（Models）：数据结构和关系

每一层都应该只依赖其下方的层，不应该出现跨层调用。

## 4. 多租户开发指南

### 4.1 租户上下文感知

始终要注意当前请求可能处于中央或租户上下文中：

```php
// 检查是否在租户上下文中
if (tenancy()->initialized) {
    // 获取当前租户
    $tenant = tenant();
} else {
    // 中央上下文逻辑
}
```

### 4.2 数据库隔离

1. **中央/租户迁移分离**:
   - 中央数据库迁移放在 `database/migrations` 目录
   - 租户数据库迁移放在 `database/migrations/tenant` 目录

2. **模块化迁移**:
   - 成绩模块迁移放在 `database/migrations/tenant/score` 目录
   - 工资模块迁移放在 `database/migrations/tenant/salary` 目录

3. **迁移命名规范**:
   - 使用标准 Laravel 迁移命名格式，如 `2024_06_12_create_scores_table.php`
   - 模块迁移前缀使用模块名，如 `2024_06_12_score_create_exams_table.php`

### 4.3 路由定义

区分中央和租户路由：

```php
// routes/web.php - 中央路由

Route::middleware(['web'])
    ->group(function () {
        // 登录和注册路由
        Route::get('/', [CentralController::class, 'index']);
        Route::get('/register', [RegisterController::class, 'showForm']);
        // ...       
    });

// routes/tenant.php - 租户路由

// 成绩模块路由
Route::middleware(['web', 'auth', 'module:score'])
    ->prefix('scores')
    ->name('scores.')
    ->group(function () {
        Route::get('/', [ScoreController::class, 'index']);
        // ...
    });

// 工资模块路由
Route::middleware(['web', 'auth', 'module:salary'])
    ->prefix('salary')
    ->name('salary.')
    ->group(function () {
        Route::get('/', [SalaryController::class, 'index']);
        // ...
    });
```

### 4.4 租户事件监听

使用事件监听器处理租户生命周期事件：

```php
<?php

namespace App\Tenancy\Listeners;

use Stancl\Tenancy\Events\TenantCreated;

class InitializeTenant
{
    public function handle(TenantCreated $event): void
    {
        $tenant = $event->tenant;
        
        // 根据租户订阅状态运行不同的初始化逻辑
        if ($tenant->hasModuleAccess('score')) {
            // 初始化成绩模块数据
        }
        
        if ($tenant->hasModuleAccess('salary')) {
            // 初始化工资模块数据
        }
    }
}
```

## 5. API 开发规范

### 5.1 RESTful API 设计

1. **命名规范**:
   - 使用名词复数形式表示资源集合（如 `/api/scores`）
   - 使用 ID 表示单个资源（如 `/api/scores/123`）
   - 使用小写字母和连字符命名路径（如 `/api/score-analytics`）

2. **HTTP 方法使用**:
   - GET: 获取资源
   - POST: 创建资源
   - PUT/PATCH: 更新资源
   - DELETE: 删除资源

3. **状态码使用**:
   - 200: 请求成功
   - 201: 资源创建成功
   - 400: 请求参数错误
   - 401: 未授权
   - 403: 禁止访问
   - 404: 资源不存在
   - 422: 请求数据验证失败
   - 500: 服务器错误

### 5.2 API 数据格式

1. **响应格式**:
   ```json
   {
     "data": {
       // 主要数据
     },
     "meta": {
       "pagination": {
         "total": 100,
         "per_page": 15,
         "current_page": 1,
         "last_page": 7
       }
     },
     "links": {
       "first": "https://example.com/api/resource?page=1",
       "last": "https://example.com/api/resource?page=7",
       "next": "https://example.com/api/resource?page=2",
       "prev": null
     }
   }
   ```

2. **错误响应格式**:
   ```json
   {
     "error": {
       "code": "validation_error",
       "message": "The given data was invalid.",
       "details": {
         "name": [
           "The name field is required."
         ]
       }
     }
   }
   ```

### 5.3 API 文档化

使用 OpenAPI/Swagger 标准注释所有 API 端点：

```php
/**
 * @OA\Get(
 *     path="/api/scores",
 *     summary="获取成绩列表",
 *     tags={"scores"},
 *     @OA\Parameter(
 *         name="student_id",
 *         in="query",
 *         description="学生 ID 筛选",
 *         @OA\Schema(type="integer")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="成功获取成绩列表",
 *         @OA\JsonContent(ref="#/components/schemas/ScoreCollection")
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="未授权"
 *     )
 * )
 */
public function index(Request $request)
{
    // ...
}
```

## 6. 测试规范

### 6.1 单元测试

应为所有服务类编写单元测试，采用以下命名规则：

```php
<?php

namespace Tests\Unit\Services\Score;

use App\Services\Score\ScoreAnalyticsService;
use Tests\TestCase;

class ScoreAnalyticsServiceTest extends TestCase
{
    /** @test */
    public function it_calculates_class_average_score_correctly()
    {
        // Arrange
        $service = $this->createMockedService();
        
        // Act
        $result = $service->calculateClassAverage($classId, $examId);
        
        // Assert
        $this->assertEquals(85.5, $result);
    }
    
    private function createMockedService()
    {
        // 模拟依赖并返回服务实例
    }
}
```

### 6.2 功能测试

在多租户环境中功能测试需要特别注意：

```php
<?php

namespace Tests\Feature\API\Score;

use App\Models\Tenant;
use App\Models\User;
use Stancl\Tenancy\Testing\TenantTestCase;
use Tests\TestCase;

class ScoreApiTest extends TenantTestCase
{
    protected $tenant;
    protected $user;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建并初始化租户
        $this->tenant = Tenant::create(['id' => 'test']);
        $this->tenant->domains()->create(['domain' => 'test.localhost']);
        tenancy()->initialize($this->tenant);
        
        // 创建测试用户
        $this->user = User::factory()->create();
    }
    
    /** @test */
    public function it_returns_student_scores()
    {
        // 模拟订阅成绩模块
        $this->mockSubscription($this->tenant, 'price_score');
        
        // 登录用户
        $this->actingAs($this->user);
        
        // 发送 API 请求
        $response = $this->getJson('/api/scores');
        
        // 断言
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'student_id', 'exam_id', 'subject_id', 'score']
                ]
            ]);
    }
    
    /** @test */
    public function it_denies_access_when_module_not_subscribed()
    {
        // 模拟订阅工资模块（不包含成绩模块）
        $this->mockSubscription($this->tenant, 'price_salary');
        
        // 登录用户
        $this->actingAs($this->user);
        
        // 发送 API 请求
        $response = $this->getJson('/api/scores');
        
        // 断言访问被拒绝
        $response->assertStatus(403)
            ->assertJson([
                'error' => [
                    'message' => 'Subscription required'
                ]
            ]);
    }
    
    private function mockSubscription($tenant, $priceId)
    {
        // 实现模拟租户订阅逻辑...
    }
}
```

## 7. 安全最佳实践

### 7.1 数据验证

始终使用请求验证类处理输入验证：

```php
<?php

namespace App\Http\Requests\Score;

use Illuminate\Foundation\Http\FormRequest;

class StoreScoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        // 检查用户权限
        return $this->user()->can('create', Score::class);
    }
    
    public function rules(): array
    {
        return [
            'student_id' => ['required', 'exists:students,id'],
            'exam_id' => ['required', 'exists:exams,id'],
            'subject_id' => ['required', 'exists:subjects,id'],
            'score' => ['required', 'numeric', 'min:0', 'max:100'],
            'comment' => ['nullable', 'string', 'max:500'],
        ];
    }
}
```

### 7.2 SQL 注入防护

始终使用查询构建器或 Eloquent，避免原始 SQL：

```php
// 安全的：使用 Eloquent
$students = Student::where('class_id', $classId)->get();

// 安全的：使用查询构建器
$scores = DB::table('scores')
    ->where('exam_id', $examId)
    ->where('student_id', $studentId)
    ->get();

// 不安全的：避免
$results = DB::select("SELECT * FROM scores WHERE student_id = '$studentId'"); // 注入风险!
```

### 7.3 CSRF 安全

所有非 GET 请求都应包含 CSRF 令牌：

```blade
<form method="POST" action="/scores">
    @csrf
    <!-- 表单字段... -->
    <button type="submit">提交</button>
</form>
```

在前端 JS 中：

```javascript
// 在 axios 配置中设置 CSRF 令牌
axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
```

### 7.4 XSS 防护

始终转义输出：

```blade
<!-- 安全的 -->
<div>{{ $userInput }}</div>

<!-- 不安全的，避免 -->
<div>{!! $userInput !!}</div> <!-- XSS 风险! -->
```

在 Vue 组件中：

```vue
<!-- 安全的 -->
<div>{{ userInput }}</div>

<!-- 不安全的，避免 -->
<div v-html="userInput"></div> <!-- XSS 风险! -->
```

## 8. 性能最佳实践

### 8.1 数据库优化

1. **查询优化**:
   - 使用 Eloquent 关联加载而非 N+1 查询
   ```php
   // 好的做法
   $scores = Score::with(['student', 'exam', 'subject'])->get();
   
   // 避免 N+1 查询
   $scores = Score::all();
   foreach ($scores as $score) {
       $student = $score->student; // 每次循环都会执行查询！
   }
   ```

2. **索引使用**:
   - 对频繁查询的字段添加索引
   - 创建复合索引以支持复杂查询

3. **数据库事务**:
   - 使用事务处理复杂操作
   ```php
   DB::transaction(function () {
       // 多个相关的数据库操作
   });
   ```

### 8.2 缓存策略

1. **数据查询缓存**:
   ```php
   $scores = Cache::remember('class_'.$classId.'_scores', 3600, function () use ($classId) {
       return Score::where('class_id', $classId)->get();
   });
   ```

2. **模块访问缓存**:
   ```php
   // 在 Tenant 模型中
   public function hasModuleAccess($module): bool
   {
       return Cache::remember('tenant_'.$this->id.'_module_'.$module, 3600, function () use ($module) {
           // 检查订阅状态
       });
   }
   ```

3. **订阅变更时清除缓存**:
   ```php
   public function handleSubscriptionUpdated(array $payload): void
   {
       $tenant = $this->findTenantByStripeId($payload['data']['object']['customer']);
       if ($tenant) {
           Cache::forget("tenant_{$tenant->id}_modules");
       }
   }
   ```

### 8.3 API 响应优化

1. **字段筛选**：允许客户端选择需要的字段
   ```php
   public function index(Request $request)
   {
       $query = Score::query();
       
       if ($fields = $request->input('fields')) {
           $query->select(explode(',', $fields));
       }
       
       return ScoreResource::collection($query->paginate());
   }
   ```

2. **关联资源包含**：允许客户端指定关联资源
   ```php
   public function index(Request $request)
   {
       $query = Score::query();
       
       if ($includes = $request->input('include')) {
           $query->with(explode(',', $includes));
       }
       
       return ScoreResource::collection($query->paginate());
   }
   ```

## 9. AI 开发者专用指南

以下是针对使用 AI 进行开发的特别注意事项：

### 9.1 数据库模式理解

开发前应详细理解以下数据库设计：

1. **中央/租户数据库分离**:
   - 中央数据库存储租户元数据和订阅信息
   - 租户数据库存储租户特定的业务数据

2. **要理解的关键表**:
   - `tenants`: 租户信息
   - `domains`: 租户域名
   - `subscriptions`/`subscription_items`: 订阅信息
   - `users`: 租户中的用户
   - `scores`/`exams`: 成绩模块主要表
   - `salaries`/`employees`: 工资模块主要表

### 9.2 定制原则与可变因素

开发时应考虑定制的可能性：

1. **模块配置的可变性**:
   - 每个租户可能订阅不同的模块组合
   - 代码应通过 `module` 中间件或 `isModuleAvailable` 检查进行适配

2. **业务逻辑的可变性**:
   - 不同学校/租户可能有不同的成绩计算或工资系数
   - 考虑使用策略模式和可配置的规则引擎

### 9.3 代码生成简化原则

自动生成代码时应遵循的原则：

1. **模式优先**:
   - 优先生成符合现有模式的代码
   - 分析现有类似文件的结构和应用相同模式

2. **带注释生成**:
   - 生成的代码应包含清晰的注释
   - 特别注释与多租户/模块相关的逻辑

3. **生成分段代码**:
   - 生成小单元代码而非大型类
   - 每个生成的组件都有清晰的单一职责

### 9.4 多租户架构注意点

开发多租户代码的特殊考虑：

1. **上下文切换检查**:
   - 所有数据库操作前都应考虑当前是否在租户上下文
   - 在实用模块时始终检查 `isModuleAvailable`

2. **数据隔离意识**:
   - 在中央控制台中，可以访问所有租户数据
   - 在租户上下文中，只能访问当前租户数据

3. **同步/异步任务处理**:
   - 在队列处理程序中注意保存和恢复租户上下文
   ```php
   Queue::push(function () use ($tenant) {
       tenancy()->initialize($tenant);
       // 异步处理逻辑...
   });
   ```

## 10. 发布和变更管理

### 10.1 版本控制

使用 Git 版本控制并遵循以下规范：

1. **分支管理**:
   - `main`: 生产环境分支，始终保持稳定
   - `develop`: 开发环境分支，集成最新特性
   - `feature/*`: 功能开发分支，如 `feature/score-analytics`
   - `hotfix/*`: 紧急修复分支，如 `hotfix/auth-issue`

2. **提交消息规范**:
   ```
   <type>(<scope>): <summary>

   <description>
   ```
   其中类型包括：
   - `feat`: 新功能
   - `fix`: 错误修复
   - `docs`: 文档变更
   - `refactor`: 重构代码
   - `test`: 测试相关
   - `chore`: 其他变更

### 10.2 发布流程

使用 CI/CD 流程进行自动化测试和部署：

1. **自动化测试**:
   - 每次提交都触发单元测试
   - 合并到 `develop` 触发所有测试和代码质量检查

2. **自动化部署**:
   - 引入环境部署 `develop` 分支
   - 验收环境部署 `release` 标签
   - 生产环境部署 `main` 分支

### 10.3 文档维护

确保维护最新的文档：

1. **代码注释保持更新**
2. **API文档随代码更新**
3. **功能模块文档随功能发布**

## 11. 总结

本开发规范指南旗在提供一致的开发标准和最佳实践，帮助团队高效地开发和维护 EduSaaS 系统。虽然标准很重要，但始终遵循实用主义原则，在尽量遵循标准的同时，也要根据具体情况进行灵活调整。

请定期审查和更新本指南，以确保其与项目的发展保持一致。