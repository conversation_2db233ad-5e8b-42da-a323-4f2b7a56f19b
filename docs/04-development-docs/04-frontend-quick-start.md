# 前端快速入门指南

> **文档职责**: 本文档为前端开发者提供快速入门指南和基础开发规范。
> 如需了解完整前端架构，请参考 [前端架构文档](01-frontend-architecture.md)；
> 如需了解详细组件实现，请参考 [前端组件参考](07-frontend-component-reference.md)。

## 开发环境要求

在开始开发之前，请确保您的环境满足以下要求：

- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0 (推荐) 或 npm >= 9.0.0
- **PHP**: >= 8.2
- **Composer**: >= 2.0

## 快速开始

### 1. 项目设置

```bash
# 克隆项目
git clone [项目地址]
cd scoreDB25

# 安装后端依赖
composer install

# 安装前端依赖
pnpm install
# 或
npm install

# 复制环境配置
cp .env.example .env

# 生成应用密钥
php artisan key:generate

# 运行数据库迁移
php artisan migrate

# 启动开发服务器
php artisan serve

# 在新终端中启动前端开发服务器
pnpm dev
# 或
npm run dev
```

### 2. shadcn-vue 组件使用

系统使用 shadcn-vue 作为 UI 组件库。以下是常用组件的使用方法：

```vue
<template>
  <div class="p-6">
    <!-- 按钮组件 -->
    <Button variant="default" size="md" @click="handleClick">
      点击我
    </Button>
    
    <!-- 输入框组件 -->
    <Input
      v-model="formData.name"
      placeholder="请输入姓名"
      class="mt-4"
    />
    
    <!-- 选择器组件 -->
    <Select v-model="formData.type">
      <SelectTrigger class="mt-4">
        <SelectValue placeholder="选择类型" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="student">学生</SelectItem>
        <SelectItem value="teacher">教师</SelectItem>
      </SelectContent>
    </Select>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const formData = ref({
  name: '',
  type: ''
})

const handleClick = () => {
  console.log('按钮被点击')
}
</script>
```

## 技术栈

- **Vue 3**: 使用Vue 3 Composition API开发前端界面
- **shadcn-vue**: 现代化UI组件库，基于Radix Vue和Tailwind CSS
- **TypeScript**: 提供类型检查和更好的开发体验
- **Inertia.js**: 将Laravel后端和Vue前端无缝连接
- **Vue Router**: 用于单页应用前端路由

## 分数分析模块前端结构

分数分析模块的前端由以下几个主要部分组成：

### 路由结构

前端路由定义在 `resources/js/routes.ts` 文件中，包含以下几个主要路由：

- `/tenant/exams`: 考试列表页面
- `/tenant/exams/:id`: 考试详情页面
- `/tenant/scores/exam/:id`: 考试分数管理页面
- `/tenant/thresholds/exam/:id`: 考试阈值管理页面

### 页面组件

主要页面组件包括：

1. **考试列表 (ExamIndex.vue)**:
   - 显示所有考试列表
   - 提供创建新考试的功能
   - 提供进入考试详情、分数管理和阈值管理的入口

2. **考试详情 ([id].vue)**:
   - 显示考试的基本信息和科目列表
   - 提供编辑考试基本信息的功能
   - 提供进入分数管理和阈值管理的入口

3. **分数管理 (scores/exam/[id].vue)**:
   - 显示学生分数列表，支持输入和修改分数
   - 提供分数分析和统计图表
   - 显示班级排名信息
   - 支持按科目筛选分数数据

4. **阈值管理 (thresholds/exam/[id].vue)**:
   - 显示考试的各科目阈值设置（如一本线、二本线等）
   - 支持添加、编辑和删除阈值
   - 支持批量配置阈值
   - 显示达到阈值的学生列表

### 布局组件

- **TenantLayout.vue**: 租户前端的通用布局，包含侧边栏和顶部导航

### 数据管理

所有页面与后端API交互，通过以下方式获取和提交数据：

- 使用Axios发送HTTP请求
- 通过Vue的响应式系统管理状态
- 使用计算属性处理派生数据

## 用户界面特点

1. **响应式设计**:
   - 适配桌面和移动设备
   - 使用Tailwind CSS实现灵活布局

2. **交互体验**:
   - 实时表单验证
   - 响应式数据更新
   - 友好的用户通知（使用Toast组件）

3. **数据可视化**:
   - 简单的分数分析图表
   - 成绩分布和排名显示
   - 直观的阈值达成学生统计

## 开发规范

1. **代码组织**:
   - 页面组件放在 `resources/js/pages/tenant/` 目录下
   - 通用组件放在 `resources/js/components/` 目录下
   - 使用类型接口定义数据结构

2. **命名约定**:
   - 组件使用PascalCase命名（如ExamIndex, TenantLayout）
   - 方法和变量使用camelCase命名（如loadExams, handleScoreInput）
   - 类型和接口使用PascalCase命名（如Exam, Score）

3. **代码风格**:
   - 使用Composition API组织代码逻辑
   - 将相关功能分组在一起
   - 使用异步函数处理API请求

## 待开发功能

分数分析模块的前端仍有以下功能待实现：

1. **报告生成功能**:
   - 学生个人成绩单生成
   - 班级成绩分析报告
   - 年级总体情况报告

2. **数据导出功能**:
   - 成绩数据导出为Excel
   - 分析报表导出为PDF

3. **高级数据可视化**:
   - 更丰富的成绩分析图表
   - 趋势分析和历史比较
   - 学生成长曲线