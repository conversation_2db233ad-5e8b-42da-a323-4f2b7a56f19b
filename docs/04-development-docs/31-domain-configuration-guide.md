# 域名配置指南

## 概述
教学事务管理系统支持灵活的域名配置，可以适应不同的部署环境和业务需求。本文档详细说明如何配置和切换不同的域名。

## 域名架构模式

### 标准域名模式
```
主域名: {domain}
管理后台: hiddenadmin.{domain}
租户域名: {subdomain}.{domain}
```

### 示例对比

#### 开发环境
- 主域名: `scoredb25.ddev.site`
- 管理后台: `hiddenadmin.scoredb25.ddev.site`
- 租户示例: `school1.scoredb25.ddev.site`

#### 生产环境示例1
- 主域名: `goodsaleabcd.com`
- 管理后台: `hiddenadmin.goodsaleabcd.com`
- 租户示例: `school1.goodsaleabcd.com`

#### 生产环境示例2
- 主域名: `edusystem.net`
- 管理后台: `hiddenadmin.edusystem.net`
- 租户示例: `school1.edusystem.net`

## 配置步骤

### 1. 环境变量配置

#### 开发环境 (.env)
```bash
# 基础域名配置
APP_URL="https://scoredb25.ddev.site"
APP_DOMAIN="scoredb25.ddev.site"
ADMIN_DOMAIN="hiddenadmin.scoredb25.ddev.site"

# Tenancy 配置
TENANCY_CENTRAL_DOMAINS="scoredb25.ddev.site,www.scoredb25.ddev.site,hiddenadmin.scoredb25.ddev.site"
```

#### 生产环境 (.env)
```bash
# 基础域名配置
APP_URL="https://goodsaleabcd.com"
APP_DOMAIN="goodsaleabcd.com"
ADMIN_DOMAIN="hiddenadmin.goodsaleabcd.com"

# Tenancy 配置
TENANCY_CENTRAL_DOMAINS="goodsaleabcd.com,www.goodsaleabcd.com,hiddenadmin.goodsaleabcd.com"
```

### 2. Laravel 配置文件

#### config/app.php
```php
<?php

return [
    // ... 其他配置

    'url' => env('APP_URL', 'http://localhost'),
    
    // 自定义域名配置
    'domain' => env('APP_DOMAIN', 'localhost'),
    'central_domain' => env('APP_DOMAIN', 'localhost'),
    'admin_domain' => env('ADMIN_DOMAIN', 'admin.localhost'),
    
    // ... 其他配置
];
```

#### config/tenancy.php
```php
<?php

return [
    // ... 其他配置
    
    'central_domains' => explode(',', env('TENANCY_CENTRAL_DOMAINS', 'localhost')),
    
    // ... 其他配置
];
```

### 3. 路由配置

#### routes/web.php (中央路由)
```php
<?php

// 使用配置中的域名
Route::domain(config('app.central_domain'))->group(function () {
    Route::get('/', 'HomeController@index')->name('home');
    Route::get('/about', 'AboutController@index')->name('about');
    
    // 认证路由
    Route::get('/login', 'Auth\LoginController@showLoginForm')->name('login');
    Route::post('/login', 'Auth\LoginController@login');
    Route::post('/logout', 'Auth\LoginController@logout')->name('logout');
    
    // 注册路由
    Route::get('/register', 'Auth\RegisterController@showRegistrationForm')->name('register');
    Route::post('/register', 'Auth\RegisterController@register');
    
    // 订阅管理
    Route::middleware('auth')->group(function () {
        Route::get('/subscription', 'SubscriptionController@index')->name('subscription.index');
        Route::post('/subscription/create', 'SubscriptionController@create')->name('subscription.create');
        Route::get('/tenant/setup', 'TenantSetupController@index')->name('tenant.setup');
        Route::post('/tenant/setup', 'TenantSetupController@store')->name('tenant.store');
    });
});
```

#### routes/admin.php (管理后台路由)
```php
<?php

// 使用配置中的管理域名
Route::domain(config('app.admin_domain'))->group(function () {
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/login', 'Admin\LoginController@showLoginForm')->name('login');
        Route::post('/login', 'Admin\LoginController@login');
        Route::post('/logout', 'Admin\LoginController@logout')->name('logout');
        
        Route::middleware('admin.auth')->group(function () {
            Route::get('/dashboard', 'Admin\DashboardController@index')->name('dashboard');
            Route::resource('/tenants', 'Admin\TenantController');
            Route::resource('/subscriptions', 'Admin\SubscriptionController');
            Route::resource('/plans', 'Admin\PlanController');
        });
    });
});
```

### 4. DNS 配置

#### 开发环境 (ddev)
```yaml
# .ddev/config.yaml
name: scoredb25
type: laravel
docroot: public
php_version: "8.4"
database:
  type: postgres
  version: "17"

additional_hostnames:
  - hiddenadmin.scoredb25
  - "*.scoredb25"  # 通配符支持所有子域名

additional_fqdns:
  - scoredb25.ddev.site
  - www.scoredb25.ddev.site
  - hiddenadmin.scoredb25.ddev.site
  - default.scoredb25.ddev.site
  - test.scoredb25.ddev.site
```

#### 生产环境 DNS 记录
```
# A 记录
goodsaleabcd.com                    A    *************
www.goodsaleabcd.com               A    *************
hiddenadmin.goodsaleabcd.com       A    *************

# 通配符记录（支持所有子域名）
*.goodsaleabcd.com                 A    *************
```

### 5. SSL 证书配置

#### 通配符证书（推荐）
```bash
# 申请通配符证书
certbot certonly --dns-cloudflare \
  --dns-cloudflare-credentials ~/.secrets/cloudflare.ini \
  -d goodsaleabcd.com \
  -d *.goodsaleabcd.com
```

#### Nginx 配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name goodsaleabcd.com www.goodsaleabcd.com;
    
    ssl_certificate /etc/letsencrypt/live/goodsaleabcd.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/goodsaleabcd.com/privkey.pem;
    
    root /var/www/html/public;
    index index.php;
    
    # Laravel 配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}

server {
    listen 443 ssl http2;
    server_name *.goodsaleabcd.com;
    
    ssl_certificate /etc/letsencrypt/live/goodsaleabcd.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/goodsaleabcd.com/privkey.pem;
    
    root /var/www/html/public;
    index index.php;
    
    # 租户和管理后台配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 域名切换流程

### 从开发环境切换到生产环境

1. **更新环境变量**
```bash
# 备份当前配置
cp .env .env.backup

# 更新域名配置
sed -i 's/scoredb25.ddev.site/goodsaleabcd.com/g' .env
sed -i 's/hiddenadmin.scoredb25.ddev.site/hiddenadmin.goodsaleabcd.com/g' .env
```

2. **清除缓存**
```bash
php artisan config:clear
php artisan route:clear
php artisan cache:clear
```

3. **更新数据库中的域名记录**
```php
// 在 tinker 中执行
php artisan tinker

// 更新现有租户的域名
use App\Models\Domain;

Domain::where('domain', 'like', '%.scoredb25.ddev.site')
    ->get()
    ->each(function ($domain) {
        $newDomain = str_replace('scoredb25.ddev.site', 'goodsaleabcd.com', $domain->domain);
        $domain->update(['domain' => $newDomain]);
    });
```

4. **验证配置**
```bash
# 检查路由
php artisan route:list

# 检查配置
php artisan config:show app.domain
php artisan config:show tenancy.central_domains
```

## 最佳实践

### 1. 域名管理
- 使用环境变量管理域名配置
- 避免在代码中硬编码域名
- 使用通配符 SSL 证书简化管理

### 2. 安全考虑
- 管理后台使用不可猜测的子域名
- 配置适当的 CORS 策略
- 定期更新 SSL 证书

### 3. 监控和维护
- 监控域名解析状态
- 设置 SSL 证书过期提醒
- 定期检查 DNS 配置

### 4. 备份和恢复
- 备份域名配置文件
- 记录 DNS 配置信息
- 建立域名切换的标准流程

## 故障排除

### 常见问题

1. **子域名无法访问**
   - 检查 DNS 通配符记录
   - 验证 SSL 证书覆盖范围
   - 确认 Nginx/Apache 配置

2. **租户识别失败**
   - 检查 tenancy 配置
   - 验证中间件配置
   - 查看应用日志

3. **跨域问题**
   - 配置 CORS 中间件
   - 检查 session 域名设置
   - 验证 cookie 配置

### 调试命令
```bash
# 检查当前域名配置
php artisan config:show app
php artisan config:show tenancy

# 测试域名解析
nslookup goodsaleabcd.com
nslookup hiddenadmin.goodsaleabcd.com
nslookup school1.goodsaleabcd.com

# 检查 SSL 证书
openssl s_client -connect goodsaleabcd.com:443 -servername goodsaleabcd.com
```

## 总结
通过灵活的域名配置机制，系统可以轻松适应不同的部署环境和业务需求。遵循本指南的配置步骤，可以确保域名切换的顺利进行，同时保持系统的稳定性和安全性。
