# 订阅模块开发指南（更新版）

## 1. 概述

scoreDB25系统采用模块化订阅机制，使租户能够根据需求选择订阅单一模块或完整系统。本文档提供订阅模块的开发指南，包括架构设计、数据模型、权限控制和前端实现等方面。系统使用 Laravel Cashier 集成 Stripe 支付，实现订阅管理和计费功能。

### 1.1 模块化设计理念

教学事务管理系统采用模块化设计，将系统功能划分为多个独立模块，实现以下目标：

1. **功能独立性**：每个模块都可以独立运行、管理和测试
2. **按需订阅**：租户可根据实际需求订阅特定功能模块
3. **成本优化**：避免为未使用的功能付费
4. **扩展灵活性**：支持系统功能的持续扩展和升级

### 1.2 核心模块定义

系统包含以下核心模块：

1. **核心模块 (core)** - 所有租户必备的基础功能，包括：
   - 用户管理与身份验证
   - 角色与权限管理
   - 基础数据管理（学生、教师、班级等）
   - 系统设置和配置

2. **成绩分析模块 (score)** - 提供完整的学生成绩管理功能，包括：
   - 考试管理和成绩录入
   - 成绩统计与分析
   - 趋势分析和排名计算
   - 分析报告生成和导出

3. **工资管理模块 (salary)** - 提供员工薪资管理功能，包括：
   - 工资结构配置和计算
   - 工资单生成与审批
   - 工资统计分析
   - 薪资报表和查询功能

### 1.3 技术实现特点

- **数据库层面**：模块化迁移系统，按模块组织数据库结构
- **路由层面**：模块化路由系统，动态加载租户订阅的模块路由
- **权限层面**：基于模块的权限系统，自动管理模块相关权限
- **前端层面**：动态界面渲染，根据订阅状态显示功能模块

## 2. 订阅架构

### 2.1 订阅模型

系统提供四种订阅计划：

1. **基础版** (`price_basic`)
   - 仅包含基本用户管理功能
   - 适合试用或初步评估

2. **成绩分析版** (`price_score`)
   - 基础版 + 成绩分析系统的完整功能
   - 适合仅需学生成绩管理的学校

3. **工资管理版** (`price_salary`)
   - 基础版 + 工资查询统计系统的完整功能
   - 适合仅需薪资管理的学校

4. **完整版** (`price_complete`)
   - 所有功能模块的完整访问权限
   - 成绩与薪资系统的数据协同和高级报表

这些订阅计划将在 `config/subscription_plans.php` 中定义：

```php
<?php

return [
    // 基础版
    'price_basic' => [
        'name' => '基础版',
        'description' => '基本用户管理功能',
        'modules' => ['core'],
        'stripe_id' => env('STRIPE_PRICE_BASIC', 'price_basic')
    ],

    // 成绩分析版
    'price_score' => [
        'name' => '成绩分析版',
        'description' => '包含完整的成绩分析系统',
        'modules' => ['core', 'score'],
        'stripe_id' => env('STRIPE_PRICE_SCORE', 'price_score')
    ],

    // 工资管理版
    'price_salary' => [
        'name' => '工资管理版',
        'description' => '包含完整的工资查询统计系统',
        'modules' => ['core', 'salary'],
        'stripe_id' => env('STRIPE_PRICE_SALARY', 'price_salary')
    ],

    // 完整版
    'price_complete' => [
        'name' => '完整版',
        'description' => '包含所有系统功能',
        'modules' => ['core', 'score', 'salary'],
        'stripe_id' => env('STRIPE_PRICE_COMPLETE', 'price_complete')
    ],
];
```

### 2.2 数据库结构

scoreDB25使用标准的Laravel Cashier 与 Stripe 集成订阅，并扩展了模块访问控制相关字段：

```sql
-- 订阅表
CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(255) REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    stripe_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_status VARCHAR(255) NOT NULL,
    stripe_price VARCHAR(255),
    module_access JSONB,  -- 存储订阅可访问的模块列表
    quantity INTEGER,
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    ends_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 订阅项目表
CREATE TABLE subscription_items (
    id SERIAL PRIMARY KEY,
    subscription_id BIGINT REFERENCES subscriptions(id) ON DELETE CASCADE,
    stripe_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_product VARCHAR(255) NOT NULL,
    stripe_price VARCHAR(255) NOT NULL,
    quantity INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 租户表扩展
ALTER TABLE tenants ADD COLUMN subscribed_modules JSONB;  -- 存储租户订阅的模块列表
```

## 3. 功能模块访问控制

### 3.1 模块定义

在 `config/modules.php` 中定义系统的功能模块：

```php
<?php

return [
    // 基础模块 - 所有订阅都包含
    'core' => [
        'name' => '核心功能',
        'description' => '基本用户管理和系统设置',
        'included_in' => ['price_basic', 'price_score', 'price_salary', 'price_complete'],
    ],

    // 成绩分析模块
    'score' => [
        'name' => '成绩分析',
        'description' => '完整的学生成绩管理和分析功能',
        'included_in' => ['price_score', 'price_complete'],
    ],

    // 工资管理模块
    'salary' => [
        'name' => '工资管理',
        'description' => '员工薪资计算和报表功能',
        'included_in' => ['price_salary', 'price_complete'],
    ],
];
```

### 3.2 中间件实现

创建 `EnsureModuleAccess` 中间件来控制模块访问权限：

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnsureModuleAccess
{
    public function handle(Request $request, Closure $next, $module)
    {
        $tenant = tenant();

        // 检查租户是否有权访问请求的模块
        if (!$tenant->hasModuleAccess($module)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Subscription required',
                    'message' => "Your subscription does not include access to the {$module} module"
                ], 403);
            }

            return redirect()->route('subscription.required', ['module' => $module]);
        }

        return $next($request);
    }
}
```

在 `app/Http/Kernel.php` 中注册中间件：

```php
protected $routeMiddleware = [
    // ...其他中间件
    'module' => \App\Http\Middleware\EnsureModuleAccess::class,
];
```

### 3.3 租户模型扩展

添加模块访问检查方法到 `Tenant` 模型：

```php
<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Stancl\Tenancy\Database\Concerns\HasPending;
use Laravel\Cashier\Billable;

class Tenant extends BaseTenant
{
    use HasDatabase, HasDomains, HasPending, Billable;

    // 检查租户是否有权访问指定模块
    public function hasModuleAccess($module)
    {
        // 检查模块是否存在
        $modules = config('modules');
        if (!isset($modules[$module])) {
            return false;
        }

        // 基础模块对所有租户可用
        if ($module === 'core') {
            return true;
        }

        // 如果在试用期内，允许访问所有模块
        if ($this->onTrial()) {
            return true;
        }

        // 检查订阅状态
        if (!$this->subscribed('default')) {
            return false;
        }

        // 获取当前订阅的价格ID
        $subscription = $this->subscription('default');
        $priceId = $subscription->stripe_price;

        // 获取当前订阅对应的计划
        $plans = config('subscription_plans');
        foreach ($plans as $plan => $details) {
            if ($details['stripe_id'] === $priceId && in_array($module, $details['modules'])) {
                return true;
            }
        }

        return false;
    }

    // 获取租户可用模块列表
    public function getAvailableModules()
    {
        return cache()->remember("tenant_{$this->id}_modules", now()->addHours(1), function () {
            $modules = ['core']; // 基础模块始终可用

            // 如果在试用期内，返回所有模块
            if ($this->onTrial()) {
                return array_keys(config('modules'));
            }

            // 检查订阅状态
            if ($this->subscribed('default')) {
                $subscription = $this->subscription('default');
                $priceId = $subscription->stripe_price;

                // 获取当前订阅对应的计划
                $plans = config('subscription_plans');
                foreach ($plans as $plan => $details) {
                    if ($details['stripe_id'] === $priceId) {
                        return $details['modules'];
                    }
                }
            }

            return $modules;
        });
    }

    // 获取当前订阅的显示名称
    public function getCurrentPlanName()
    {
        if ($this->onTrial()) {
            return '试用期';
        }

        if (!$this->subscribed('default')) {
            return '无订阅';
        }

        $subscription = $this->subscription('default');
        $priceId = $subscription->stripe_price;

        $plans = config('subscription_plans');
        foreach ($plans as $plan => $details) {
            if ($details['stripe_id'] === $priceId) {
                return $details['name'];
            }
        }

        return '未知计划';
    }
}
```

## 4. 路由与控制器实现

### 4.1 路由定义

在 `routes/tenant.php` 中配置基于模块的路由访问控制：

```php
<?php

use App\Http\Controllers\ScoreController;
use App\Http\Controllers\SalaryController;
use App\Http\Controllers\SubscriptionController;
use Illuminate\Support\Facades\Route;

// 订阅管理路由
Route::prefix('subscription')->name('subscription.')->group(function () {
    Route::get('/', [SubscriptionController::class, 'index'])->name('index');
    Route::post('/checkout', [SubscriptionController::class, 'checkout'])->name('checkout');
    Route::get('/success', [SubscriptionController::class, 'success'])->name('success');
    Route::get('/cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
    Route::get('/required', [SubscriptionController::class, 'required'])->name('required');
});

// 成绩分析模块路由 - 需要'score'模块访问权限
Route::middleware(['auth', 'verified', 'module:score'])
    ->prefix('scores')
    ->name('scores.')
    ->group(function () {
        Route::get('/', [ScoreController::class, 'index'])->name('index');
        Route::get('/analysis', [ScoreController::class, 'analysis'])->name('analysis');
        Route::get('/import', [ScoreController::class, 'import'])->name('import');
        Route::post('/import', [ScoreController::class, 'processImport']);
        Route::get('/reports', [ScoreController::class, 'reports'])->name('reports');
        // 其他成绩相关路由...
    });

// 工资管理模块路由 - 需要'salary'模块访问权限
Route::middleware(['auth', 'verified', 'module:salary'])
    ->prefix('salary')
    ->name('salary.')
    ->group(function () {
        Route::get('/', [SalaryController::class, 'index'])->name('index');
        Route::get('/components', [SalaryController::class, 'components'])->name('components');
        Route::get('/calculations', [SalaryController::class, 'calculations'])->name('calculations');
        Route::get('/reports', [SalaryController::class, 'reports'])->name('reports');
        // 其他工资相关路由...
    });
```

### 4.2 订阅控制器

实现 `SubscriptionController` 处理订阅相关操作：

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Laravel\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    // 渲染订阅管理页面
    public function index()
    {
        $tenant = tenant();
        $subscription = $tenant->subscription('default');
        $plans = config('subscription_plans');
        $modules = config('modules');

        return Inertia::render('Subscription/Index', [
            'tenant' => $tenant,
            'currentPlan' => $tenant->getCurrentPlanName(),
            'subscription' => $subscription,
            'onTrial' => $tenant->onTrial(),
            'trialEndsAt' => $tenant->trial_ends_at,
            'plans' => $plans,
            'modules' => $modules,
            'availableModules' => $tenant->getAvailableModules(),
        ]);
    }

    // 创建 Stripe Checkout 会话
    public function checkout(Request $request)
    {
        $tenant = tenant();
        $planKey = $request->input('plan');

        // 检查计划是否存在
        $plans = config('subscription_plans');
        if (!isset($plans[$planKey])) {
            return back()->withErrors(['message' => '无效的订阅计划']);
        }

        $plan = $plans[$planKey];
        $priceId = $plan['stripe_id'];

        try {
            $checkoutUrl = $tenant->newSubscription('default', $priceId)
                ->allowPromotionCodes()
                ->checkout([
                    'success_url' => route('subscription.success') . '?session_id={CHECKOUT_SESSION_ID}',
                    'cancel_url' => route('subscription.cancel'),
                ]);

            return redirect($checkoutUrl);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    // 支付成功回调
    public function success(Request $request)
    {
        $tenant = tenant();

        return Inertia::render('Subscription/Success', [
            'tenant' => $tenant,
            'subscription' => $tenant->subscription('default'),
            'currentPlan' => $tenant->getCurrentPlanName(),
        ]);
    }

    // 支付取消回调
    public function cancel()
    {
        return Inertia::render('Subscription/Cancel');
    }

    // 订阅需求提示页面
    public function required(Request $request)
    {
        $module = $request->input('module');
        $modules = config('modules');
        $moduleInfo = $modules[$module] ?? null;

        if (!$moduleInfo) {
            abort(404);
        }

        // 获取包含该模块的计划
        $plans = config('subscription_plans');
        $availablePlans = [];

        foreach ($plans as $key => $plan) {
            if (in_array($module, $plan['modules'])) {
                $availablePlans[$key] = $plan;
            }
        }

        return Inertia::render('Subscription/Required', [
            'moduleInfo' => $moduleInfo,
            'module' => $module,
            'availablePlans' => $availablePlans,
        ]);
    }
}
```

## 5. 前端实现

### 5.1 导航菜单组件

创建基于模块访问权限的导航菜单：

```vue
<script setup>
import { Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
  availableModules: Array
});

const openDropdown = ref(null);

const hasModuleAccess = (module) => {
  return props.availableModules.includes(module);
};

const toggleDropdown = (name) => {
  openDropdown.value = openDropdown.value === name ? null : name;
};
</script>

<template>
  <nav class="main-nav">
    <ul>
      <li><Link href="/dashboard">首页</Link></li>

      <!-- 成绩分析模块 -->
      <li v-if="hasModuleAccess('score')" class="dropdown">
        <a href="#" @click.prevent="toggleDropdown('score')">成绩分析</a>
        <ul v-show="openDropdown === 'score'" class="dropdown-menu">
          <li><Link href="/scores">成绩概览</Link></li>
          <li><Link href="/scores/import">成绩导入</Link></li>
          <li><Link href="/scores/analysis">成绩分析</Link></li>
          <li><Link href="/scores/reports">分析报告</Link></li>
        </ul>
      </li>

      <!-- 工资管理模块 -->
      <li v-if="hasModuleAccess('salary')" class="dropdown">
        <a href="#" @click.prevent="toggleDropdown('salary')">工资管理</a>
        <ul v-show="openDropdown === 'salary'" class="dropdown-menu">
          <li><Link href="/salary">工资概览</Link></li>
          <li><Link href="/salary/components">薪资组成管理</Link></li>
          <li><Link href="/salary/calculations">薪资计算</Link></li>
          <li><Link href="/salary/reports">薪资报表</Link></li>
        </ul>
      </li>

      <!-- 系统管理 -->
      <li class="dropdown">
        <a href="#" @click.prevent="toggleDropdown('admin')">系统管理</a>
        <ul v-show="openDropdown === 'admin'" class="dropdown-menu">
          <li><Link href="/users">用户管理</Link></li>
          <li><Link href="/roles">角色权限</Link></li>
          <li><Link href="/subscription">订阅管理</Link></li>
          <li><Link href="/settings">系统设置</Link></li>
        </ul>
      </li>
    </ul>
  </nav>
</template>
```

### 5.2 订阅管理页面

创建订阅管理页面：

```vue
<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { computed } from 'vue';

const props = defineProps({
  tenant: Object,
  currentPlan: String,
  subscription: Object,
  onTrial: Boolean,
  trialEndsAt: String,
  plans: Object,
  modules: Object,
  availableModules: Array
});

const form = useForm({
  plan: null
});

const checkout = () => {
  form.post(route('subscription.checkout'));
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const getModuleName = (moduleKey) => {
  return props.modules[moduleKey]?.name || moduleKey;
};

const getModuleDescription = (moduleKey) => {
  return props.modules[moduleKey]?.description || '';
};

const subscriptionStatus = computed(() => {
  if (props.onTrial) {
    return '试用中';
  }

  if (!props.subscription) {
    return '无订阅';
  }

  if (props.subscription.ended()) {
    return '已取消';
  }

  if (props.subscription.canceled()) {
    return '将到期';
  }

  return '活跃';
});
</script>

<template>
  <AppLayout title="订阅管理">
    <Head title="订阅管理" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
          <h2 class="text-2xl font-bold mb-6">当前订阅</h2>

          <!-- 当前订阅状态 -->
          <div class="mb-8 p-6 border rounded-lg bg-gray-50">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-xl font-semibold mb-2">{{ currentPlan }}</h3>
                <p class="text-gray-600">状态: <span class="font-semibold">{{ subscriptionStatus }}</span></p>
                <template v-if="onTrial">
                  <p class="text-gray-600">试用到期日: {{ formatDate(trialEndsAt) }}</p>
                </template>
                <template v-else-if="subscription && !subscription.ended()">
                  <p class="text-gray-600">下一个计费周期: {{ formatDate(subscription.current_period_end) }}</p>
                </template>
              </div>

              <div v-if="subscription && !subscription.ended() && !subscription.canceled()">
                <Link :href="route('subscription.cancel')" class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded text-sm">
                  取消订阅
                </Link>
              </div>
            </div>
          </div>

          <!-- 可用模块 -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4">当前可用模块</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="module in availableModules" :key="module" class="p-4 border rounded-lg">
                <h4 class="font-semibold text-lg">{{ getModuleName(module) }}</h4>
                <p class="text-gray-600">{{ getModuleDescription(module) }}</p>
              </div>
            </div>
          </div>

          <!-- 可用订阅计划 -->
          <div>
            <h3 class="text-xl font-semibold mb-4">订阅计划</h3>
            <form @submit.prevent="checkout">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div v-for="(plan, key) in plans" :key="key" class="border rounded-lg p-4 relative">
                  <label class="flex flex-col h-full">
                    <input
                      type="radio"
                      v-model="form.plan"
                      :value="key"
                      class="absolute top-4 right-4"
                    />
                    <span class="text-xl font-semibold mb-2">{{ plan.name }}</span>
                    <p class="text-gray-600 mb-4 flex-grow">{{ plan.description }}</p>
                    <div class="mt-auto">
                      <h5 class="font-semibold">包含模块:</h5>
                      <ul class="text-sm">
                        <li v-for="module in plan.modules" :key="module">
                          {{ getModuleName(module) }}
                        </li>
                      </ul>
                    </div>
                  </label>
                </div>
              </div>

              <div class="mt-6">
                <button
                  type="submit"
                  class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded"
                  :disabled="!form.plan || form.processing"
                >
                  {{ subscription && !subscription.ended() ? '升级订阅' : '订阅计划' }}
                </button>
                <p v-if="form.errors.message" class="text-red-500 mt-2">{{ form.errors.message }}</p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
```

### 5.3 模块访问限制提示

创建订阅需求提示页面：

```vue
<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
  moduleInfo: Object,
  module: String,
  availablePlans: Object
});

const form = useForm({
  plan: Object.keys(props.availablePlans)[0] || null
});

const checkout = () => {
  form.post(route('subscription.checkout'));
};
</script>

<template>
  <AppLayout title="订阅需求">
    <Head title="订阅需求" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold">请升级您的订阅</h2>
            <p class="mt-2 text-gray-600">访问 {{ moduleInfo.name }} 模块需要升级您的订阅计划</p>
          </div>

          <div class="mb-8 p-6 border rounded-lg bg-gray-50">
            <h3 class="text-xl font-semibold mb-4">{{ moduleInfo.name }}</h3>
            <p>{{ moduleInfo.description }}</p>
          </div>

          <form @submit.prevent="checkout">
            <h3 class="text-xl font-semibold mb-4">可用计划</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="(plan, key) in availablePlans" :key="key" class="border rounded-lg p-4 relative">
                <label class="flex flex-col h-full">
                  <input
                    type="radio"
                    v-model="form.plan"
                    :value="key"
                    class="absolute top-4 right-4"
                  />
                  <span class="text-xl font-semibold mb-2">{{ plan.name }}</span>
                  <p class="text-gray-600 mb-4 flex-grow">{{ plan.description }}</p>
                </label>
              </div>
            </div>

            <div class="mt-6 flex justify-between">
              <Link href="/dashboard" class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-6 rounded">
                返回首页
              </Link>

              <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded"
                :disabled="!form.plan || form.processing"
              >
                继续订阅
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
```

## 6. Webhook 处理

创建 Stripe Webhook 控制器来处理订阅事件：

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Laravel\Cashier\Http\Controllers\WebhookController as CashierWebhookController;
use Illuminate\Support\Facades\Log;
use App\Models\Tenant;

class WebhookController extends CashierWebhookController
{
    /**
     * 处理订阅状态变更
     */
    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        parent::handleCustomerSubscriptionUpdated($payload);

        // 获取租户
        $tenant = $this->findTenantByStripeId($payload['data']['object']['customer']);

        // 如果找到租户，更新相关缓存或状态
        if ($tenant) {
            // 清除模块访问缓存
            cache()->forget("tenant_{$tenant->id}_modules");

            // 记录日志
            Log::info('Subscription updated for tenant: ' . $tenant->id, [
                'tenant_id' => $tenant->id,
                'subscription' => $payload['data']['object']
            ]);
        }
    }

    /**
     * 处理订阅取消事件
     */
    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        parent::handleCustomerSubscriptionDeleted($payload);

        // 获取租户
        $tenant = $this->findTenantByStripeId($payload['data']['object']['customer']);

        // 如果找到租户，更新相关状态
        if ($tenant) {
            // 清除模块访问缓存
            cache()->forget("tenant_{$tenant->id}_modules");

            // 记录日志
            Log::info('Subscription canceled for tenant: ' . $tenant->id, [
                'tenant_id' => $tenant->id,
                'subscription' => $payload['data']['object']
            ]);
        }
    }

    /**
     * 根据Stripe ID查找租户
     */
    protected function findTenantByStripeId($stripeId)
    {
        return Tenant::where('stripe_id', $stripeId)->first();
    }
}
```

## 7. 测试与部署

### 7.1 配置 Stripe 环境

1. 在 `.env` 文件中配置 Stripe 密钥：

```
STRIPE_KEY=pk_test_xxx
STRIPE_SECRET=sk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx

# 订阅计划 ID
STRIPE_PRICE_BASIC=price_xxx
STRIPE_PRICE_SCORE=price_xxx
STRIPE_PRICE_SALARY=price_xxx
STRIPE_PRICE_COMPLETE=price_xxx
```

2. 在 Stripe 中创建定制产品和价格：
   - 创建四个产品（Basic、Score、Salary、Complete）
   - 为每个产品创建递归订阅价格
   - 将价格 ID 配置到环境变量中

3. 配置 Stripe Webhook：
   - 在 Stripe 中创建新的 Webhook 端点（指向你的应用的 `/stripe/webhook` 路径）
   - 订阅以下事件：`customer.subscription.created`、`customer.subscription.updated`、`customer.subscription.deleted`
   - 将生成的签名密钥添加到环境变量中

### 7.2 组件测试

创建测试用例验证模块访问权限控制：

```php
<?php

namespace Tests\Feature;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModuleAccessTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_cannot_access_score_module_without_subscription()
    {
        // 创建租户和用户
        $tenant = Tenant::factory()->create(['id' => 'test']);
        $user = User::factory()->create();

        // 切换到租户上下文
        tenancy()->initialize($tenant);

        // 以用户身份登录
        $this->actingAs($user);

        // 访问成绩模块
        $response = $this->get(route('scores.index'));

        // 应该重定向到订阅需求页面
        $response->assertRedirect(route('subscription.required', ['module' => 'score']));
    }

    public function test_user_can_access_score_module_with_score_subscription()
    {
        // 创建租户和用户
        $tenant = Tenant::factory()->create(['id' => 'test']);
        $user = User::factory()->create();

        // 切换到租户上下文
        tenancy()->initialize($tenant);

        // 模拟成绩分析订阅
        $this->mockSubscription($tenant, 'price_score');

        // 以用户身份登录
        $this->actingAs($user);

        // 访问成绩模块
        $response = $this->get(route('scores.index'));

        // 应该成功访问
        $response->assertOk();
    }

    public function test_user_cannot_access_salary_module_with_score_subscription()
    {
        // 创建租户和用户
        $tenant = Tenant::factory()->create(['id' => 'test']);
        $user = User::factory()->create();

        // 切换到租户上下文
        tenancy()->initialize($tenant);

        // 模拟成绩分析订阅
        $this->mockSubscription($tenant, 'price_score');

        // 以用户身份登录
        $this->actingAs($user);

        // 访问工资模块
        $response = $this->get(route('salary.index'));

        // 应该重定向到订阅需求页面
        $response->assertRedirect(route('subscription.required', ['module' => 'salary']));
    }

    /**
     * 模拟租户订阅
     */
    private function mockSubscription($tenant, $priceId)
    {
        // 设置租户对应的模块访问查询
        $tenant->hasModuleAccess = function ($module) use ($priceId) {
            $modules = config('modules');
            if (!isset($modules[$module])) {
                return false;
            }

            if ($module === 'core') {
                return true;
            }

            if (in_array($priceId, $modules[$module]['included_in'])) {
                return true;
            }

            return false;
        };

        // 创建订阅记录
        $subscription = $tenant->subscriptions()->create([
            'name' => 'default',
            'stripe_id' => 'sub_' . uniqid(),
            'stripe_status' => 'active',
            'stripe_price' => $priceId,
            'quantity' => 1,
            'trial_ends_at' => null,
            'ends_at' => null,
        ]);

        // 创建订阅项目
        $subscription->items()->create([
            'stripe_id' => 'si_' . uniqid(),
            'stripe_product' => 'prod_' . uniqid(),
            'stripe_price' => $priceId,
            'quantity' => 1,
        ]);
    }
}
```

## 8. 安全和注意事项

1. **Webhook 安全：** 确保 Stripe Webhook 端点使用签名密钥进行验证，防止未授权的请求。

2. **订阅数据同步：** 定期运行订阅数据同步任务，以防止 Webhook 失败导致的数据不一致。

   ```php
   // app/Console/Commands/SyncSubscriptions.php
   public function handle()
   {
       $tenants = \App\Models\Tenant::all();

       foreach ($tenants as $tenant) {
           try {
               // 同步订阅状态
               $tenant->syncStripeSubscriptions();

               // 清除模块访问缓存
               cache()->forget("tenant_{$tenant->id}_modules");

               $this->info("Synced subscription for tenant: {$tenant->id}");
           } catch (\Exception $e) {
               $this->error("Failed to sync tenant {$tenant->id}: {$e->getMessage()}");
           }
       }
   }
   ```

3. **试用期到期通知：** 实现试用期即将到期通知，提醒租户升级至付费计划。

4. **订阅变更处理：** 确保在订阅升级或降级时处理好功能访问权限的过渡。

5. **日志记录：** 对所有订阅相关的操作进行详细的日志记录，以便排错和审计。

## 9. 总结

scoreDB25 系统的模块化订阅机制使得学校可以根据自身需求选择成绩分析或工资管理功能，或者全部选择。通过集成 Laravel Cashier 和 Stripe，实现了灵活的订阅管理和付款处理。中间件、控制器和前端组件的紧密集成确保了用户体验的流畅和安全。