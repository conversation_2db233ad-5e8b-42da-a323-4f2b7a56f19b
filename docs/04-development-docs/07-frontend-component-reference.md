# 前端组件参考文档

> **文档职责**: 本文档提供详细的前端组件库实现参考、代码示例和技术细节。
> 如需了解前端架构概览，请参考 [前端架构文档](01-frontend-architecture.md)；
> 如需快速入门，请参考 [前端快速入门指南](04-frontend-quick-start.md)。

## 1. 概述

EduSaaS 系统前端采用现代化的组件库架构，基于 Vue 3 + shadcn-vue 构建，为成绩分析系统和工资管理系统提供统一、一致的用户界面体验。本文档详细说明组件库的设计与实现，包括基础组件、业务组件、状态管理以及与后端 API 的集成方式。

## 2. 技术栈

### 2.1 核心技术

- **Vue 3**：采用 Composition API 开发组件
- **shadcn-vue**：无样式 UI 组件集合，基于 Radix Vue 和 Tailwind CSS
- **Radix Vue**：提供无障碍、可组合的基础组件
- **Tailwind CSS**：实用优先的 CSS 框架
- **TypeScript**：提供类型安全和代码提示
- **Vite**：快速的前端构建工具
- **Pinia**：Vue 3 的官方状态管理库
- **Vue Router**：Vue 的官方路由管理器

### 2.2 依赖与版本

```json
{
  "name": "edusaas-frontend",
  "version": "1.0.0",
  "type": "module",
  "dependencies": {
    "@inertiajs/vue3": "^1.0.0",
    "@radix-ui/vue": "^1.4.0",
    "@tanstack/vue-query": "^5.17.15",
    "@vueuse/core": "^10.7.1",
    "axios": "^1.6.3",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "lucide-vue-next": "^0.306.0",
    "pinia": "^2.1.7",
    "radix-vue": "^1.2.6",
    "tailwind-merge": "^2.2.1",
    "tailwindcss-animate": "^1.0.7",
    "vue": "^3.3.13",
    "vue-chartjs": "^5.3.0"
  },
  "devDependencies": {
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10",
    "@types/node": "^20.10.5",
    "@vitejs/plugin-vue": "^5.0.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32",
    "prettier": "^3.1.1",
    "prettier-plugin-tailwindcss": "^0.5.9",
    "tailwindcss": "^3.4.0",
    "typescript": "^5.3.3",
    "vite": "^5.0.10",
    "vue-tsc": "^1.8.26"
  }
}
```

## 3. 组件架构

### 3.1 组件分层

EduSaaS 前端组件采用三层架构设计：

1. **基础 UI 层**：shadcn-vue 提供的无样式基础组件，如按钮、表单控件、对话框等
2. **应用组件层**：系统通用组件，如布局、数据表格、表单等
3. **业务组件层**：特定于成绩分析或工资管理的业务组件

### 3.2 组件目录结构

```
resources/js/
├── components/
│   ├── ui/               # shadcn-vue 基础组件
│   │   ├── button.vue
│   │   ├── input.vue
│   │   ├── select.vue
│   │   └── ...
│   ├── layout/           # 布局组件
│   │   ├── app-layout.vue
│   │   ├── sidebar-nav.vue
│   │   └── ...
│   ├── common/           # 通用组件
│   │   ├── data-table.vue
│   │   ├── forms/
│   │   ├── charts/
│   │   └── ...
│   ├── score/            # 成绩分析组件
│   │   ├── score-import.vue
│   │   ├── analysis-dashboard.vue
│   │   └── ...
│   └── salary/           # 工资管理组件
│       ├── salary-structure.vue
│       ├── salary-report.vue
│       └── ...
├── composables/          # 组合式函数
├── stores/               # Pinia 状态管理
├── lib/                  # 工具函数
├── types/                # TypeScript 类型定义
└── services/             # API 服务
```

## 4. 订阅状态与模块访问控制

### 4.1 模块访问控制组件

```vue
<!-- resources/js/components/common/module-access.vue -->
<template>
  <div>
    <slot v-if="hasAccess" />
    <div v-else class="py-12 text-center">
      <h3 class="text-lg font-medium">{{ moduleInfo.name }} 访问受限</h3>
      <p class="mt-2 text-sm text-gray-500">
        您当前的订阅计划不包含对此功能的访问权限
      </p>
      <Button @click="redirectToSubscription" class="mt-4">
        升级订阅
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { router } from '@inertiajs/vue3'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface Props {
  module: 'score' | 'salary'
  isActive: boolean
  hasAccess: boolean
}

const props = defineProps<Props>()

const upgradeToAccess = () => {
  router.visit({ url: 'subscription.plans', data: { module: props.module } })
}
</script>
```

### 4.2 模块访问指令

```typescript
// resources/js/directives/module-access.ts
import { ObjectDirective } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

export const vModuleAccess: ObjectDirective<HTMLElement, string> = {
  beforeMount(el, binding) {
    const subscriptionStore = useSubscriptionStore()
    if (!subscriptionStore.hasModuleAccess(binding.value)) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const subscriptionStore = useSubscriptionStore()
    if (!subscriptionStore.hasModuleAccess(binding.value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}
```

### 4.3 订阅状态存储

```typescript
// resources/js/stores/subscription.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { SubscriptionApi } from '@/services/api/subscription'

export const useSubscriptionStore = defineStore('subscription', () => {
  const subscription = ref(null)
  const availableModules = ref([])
  const modules = ref({})
  const loading = ref(false)
  
  const isActive = computed(() => {
    return subscription.value?.status === 'active'
  })
  
  const currentPlan = computed(() => {
    return subscription.value?.price ?? null
  })
  
  async function fetchSubscriptionStatus() {
    loading.value = true
    try {
      const response = await SubscriptionApi.getStatus()
      subscription.value = response.subscription
      availableModules.value = response.availableModules
      modules.value = response.modules
    } catch (error) {
      console.error('Failed to fetch subscription status', error)
    } finally {
      loading.value = false
    }
  }
  
  function hasModuleAccess(module) {
    return availableModules.value.includes(module)
  }
  
  function getModuleInfo(module) {
    return modules.value[module] || { name: module, description: '' }
  }
  
  return {
    subscription,
    availableModules,
    modules,
    loading,
    isActive,
    currentPlan,
    fetchSubscriptionStatus,
    hasModuleAccess,
    getModuleInfo
  }
})
```

## 5. UI 组件示例

### 5.1 可复用数据表格组件

```vue
<!-- resources/js/components/common/data-table.vue -->
<template>
  <div class="w-full">
    <div class="flex items-center justify-between pb-4">
      <h2 v-if="title" class="text-lg font-semibold">{{ title }}</h2>
      <div class="flex space-x-2">
        <slot name="actions" />
      </div>
    </div>
    
    <div class="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead 
              v-for="column in columns" 
              :key="column.key"
              :class="column.class"
            >
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow 
            v-for="(row, index) in data" 
            :key="index"
            :class="{'hover:bg-primary/10': rowClickable}"
            @click="rowClickable ? $emit('row-click', row) : null"
            :style="rowClickable ? 'cursor: pointer' : ''"
          >
            <TableCell 
              v-for="column in columns" 
              :key="column.key"
              :class="column.cellClass"
            >
              <slot :name="`cell-${column.key}`" :row="row" :index="index">
                {{ getCellValue(row, column) }}
              </slot>
            </TableCell>
          </TableRow>
          <TableRow v-if="data.length === 0">
            <TableCell :colspan="columns.length" class="text-center py-6">
              <slot name="empty">
                <p class="text-sm text-gray-500">没有数据</p>
              </slot>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    
    <div v-if="pagination" class="flex justify-between items-center mt-4">
      <div class="text-sm text-gray-500">
        显示 {{ pagination.from }}-{{ pagination.to }} 条，共 {{ pagination.total }} 条
      </div>
      <div class="flex space-x-1">
        <Button 
          variant="outline" 
          size="sm"
          :disabled="!pagination.prev_page_url"
          @click="$emit('page-change', pagination.current_page - 1)"
        >
          上一页
        </Button>
        <Button 
          variant="outline" 
          size="sm"
          :disabled="!pagination.next_page_url"
          @click="$emit('page-change', pagination.current_page + 1)"
        >
          下一页
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { get } from 'lodash-es'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  columns: {
    type: Array,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  pagination: {
    type: Object,
    default: null
  },
  rowClickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['row-click', 'page-change'])

function getCellValue(row, column) {
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(get(row, column.key), row)
  }
  return get(row, column.key)
}
</script>
```

### 5.2 成绩分析图表组件

```vue
<!-- resources/js/components/score/score-chart.vue -->
<template>
  <div class="p-4 bg-white rounded-md shadow">
    <div class="flex justify-between items-center mb-4">
      <h3 class="font-medium">{{ title }}</h3>
      <div class="flex space-x-2">
        <Button 
          v-for="option in chartTypeOptions" 
          :key="option.value"
          size="sm"
          :variant="chartType === option.value ? 'default' : 'outline'"
          @click="chartType = option.value"
        >
          {{ option.label }}
        </Button>
      </div>
    </div>
    
    <div class="h-80">
      <Bar 
        v-if="chartType === 'bar'" 
        :data="chartData" 
        :options="chartOptions" 
      />
      <Line 
        v-else-if="chartType === 'line'" 
        :data="chartData" 
        :options="chartOptions" 
      />
      <Pie 
        v-else-if="chartType === 'pie'" 
        :data="chartData" 
        :options="chartOptions" 
      />
    </div>
    
    <div v-if="description" class="mt-4 text-sm text-gray-500">
      {{ description }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { Bar, Line, Pie } from 'vue-chartjs'
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale, PointElement, LineElement, ArcElement } from 'chart.js'
import { Button } from '@/components/ui/button'

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale, PointElement, LineElement, ArcElement)

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  labels: {
    type: Array,
    required: true
  },
  datasets: {
    type: Array,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  defaultType: {
    type: String,
    default: 'bar',
    validator: (value) => ['bar', 'line', 'pie'].includes(value)
  }
})

const chartType = ref(props.defaultType)

const chartTypeOptions = [
  { label: '柱状图', value: 'bar' },
  { label: '折线图', value: 'line' },
  { label: '饼图', value: 'pie' }
]

const chartData = computed(() => {
  return {
    labels: props.labels,
    datasets: props.datasets
  }
})

const chartOptions = computed(() => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom'
      }
    }
  }
})
</script>
```

### 5.3 订阅选择组件

```vue
<!-- resources/js/components/subscription/plan-selector.vue -->
<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">选择订阅计划</h2>
    
    <RadioGroup v-model="selectedPlan" class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
      <div 
        v-for="plan in plans" 
        :key="plan.id"
        class="relative rounded-lg border p-4 cursor-pointer transition-colors"
        :class="{
          'border-primary bg-primary/5': selectedPlan === plan.id,
          'border-border': selectedPlan !== plan.id
        }"
        @click="selectedPlan = plan.id"
      >
        <RadioGroupItem :value="plan.id" class="sr-only" />
        
        <div class="mb-2">
          <Badge v-if="isCurrentPlan(plan.id)" variant="outline" class="mb-2">
            当前计划
          </Badge>
          <h3 class="text-lg font-medium">{{ plan.name }}</h3>
          <div class="flex items-baseline mt-1">
            <span class="text-2xl font-bold">¥{{ plan.price }}</span>
            <span class="text-sm text-gray-500 ml-1">/月</span>
          </div>
        </div>
        
        <div class="mt-4 space-y-2">
          <div 
            v-for="feature in plan.features" 
            :key="feature"
            class="flex items-center"
          >
            <CheckIcon class="h-4 w-4 text-primary mr-2" />
            <span class="text-sm">{{ feature }}</span>
          </div>
        </div>
        
        <div class="mt-6">
          <Button 
            :disabled="isCurrentPlan(plan.id)"
            :variant="selectedPlan === plan.id ? 'default' : 'outline'"
            class="w-full"
            @click="selectPlan(plan.id)"
          >
            {{ isCurrentPlan(plan.id) ? '当前计划' : '选择计划' }}
          </Button>
        </div>
      </div>
    </RadioGroup>
    
    <div class="mt-8 text-center">
      <Button 
        variant="default" 
        size="lg"
        :disabled="!selectedPlan || isCurrentPlan(selectedPlan)"
        @click="confirmSubscription"
      >
        确认订阅
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckIcon } from '@/components/icons'

const props = defineProps({
  plans: {
    type: Array,
    required: true
  },
  recommendedModule: {
    type: String,
    default: null
  },
  currentPlan: {
    type: String,
    default: null
  },
  isActive: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['plan-selected', 'confirm'])

const selectedPlan = ref('')

// 根据推荐模块预选择计划
if (props.recommendedModule) {
  const recommendedPlan = props.plans.find(plan => {
    return plan.modules.includes(props.recommendedModule)
  })
  if (recommendedPlan) {
    selectedPlan.value = recommendedPlan.id
  }
}

function isCurrentPlan(planId) {
  return props.isActive && props.currentPlan === planId
}

function selectPlan(planId) {
  selectedPlan.value = planId
  emit('plan-selected', planId)
}

function confirmSubscription() {
  emit('confirm', selectedPlan.value)
}
</script>
```

## 6. 模块化路由结构

### 6.1 Laravel 路由配置

在 Inertia.js 应用中，路由由 Laravel 后端管理，而不是前端路由器。

```php
// routes/tenant.php
Route::middleware(['auth', 'subscription:score'])
    ->prefix('scores')
    ->name('scores.')
    ->group(function () {
        Route::get('/', [ScoreController::class, 'dashboard'])->name('dashboard');
        Route::get('/import', [ScoreController::class, 'import'])->name('import');
        Route::get('/analysis', [ScoreController::class, 'analysis'])->name('analysis');
        Route::get('/reports', [ScoreController::class, 'reports'])->name('reports');
    });
```

### 6.2 订阅中间件

```php
// app/Http/Middleware/CheckSubscription.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckSubscription
{
    public function handle(Request $request, Closure $next, string $module)
    {
        $tenant = tenant();
        
        if (!$tenant->hasModuleAccess($module)) {
            return inertia('subscription/Required', [
                'module' => $module,
                'redirect' => $request->url()
            ]);
        }
        
        return $next($request);
    }
}
```

## 7. API 集成

### 7.1 API 服务层

```typescript
// resources/js/services/api/index.ts
import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  withCredentials: true
})

// 请求拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
api.interceptors.response.use(
  response => response,
  error => {
    // 处理401未授权错误
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    
    // 处理模块访问限制错误
    if (error.response?.status === 403 && error.response?.data?.error === 'Subscription required') {
      window.location.href = `/subscription/required?module=${error.response.data.module}`
    }
    
    return Promise.reject(error)
  }
)

export default api
```

### 7.2 模块特定 API 服务

```typescript
// resources/js/services/api/score.ts
import api from './index'

export const ScoreApi = {
  /**
   * 获取成绩概览数据
   */
  getDashboard() {
    return api.get('/scores/dashboard')
  },
  
  /**
   * 导入成绩数据
   */
  importScores(formData) {
    return api.post('/scores/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 获取成绩分析数据
   */
  getAnalysis(params) {
    return api.get('/scores/analysis', { params })
  },
  
  /**
   * 获取学生成绩列表
   */
  getStudentScores(params) {
    return api.get('/scores/students', { params })
  },
  
  /**
   * 获取班级成绩统计
   */
  getClassStatistics(classId, examId) {
    return api.get(`/scores/classes/${classId}/exams/${examId}`)
  },
  
  /**
   * 获取年级成绩统计
   */
  getGradeStatistics(gradeId, examId) {
    return api.get(`/scores/grades/${gradeId}/exams/${examId}`)
  },
  
  /**
   * 生成分析报告
   */
  generateReport(params) {
    return api.post('/scores/reports', params)
  },
  
  /**
   * 获取报告列表
   */
  getReports(params) {
    return api.get('/scores/reports', { params })
  },
  
  /**
   * 获取单个报告详情
   */
  getReport(reportId) {
    return api.get(`/scores/reports/${reportId}`)
  }
}
```

## 8. 测试策略

### 8.1 单元测试

使用 Vitest 进行组件和工具函数的单元测试：

```typescript
// resources/js/components/common/__tests__/data-table.spec.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import DataTable from '../data-table.vue'

describe('DataTable', () => {
  it('renders correctly with data', () => {
    const wrapper = mount(DataTable, {
      props: {
        columns: [
          { key: 'name', label: '姓名' },
          { key: 'age', label: '年龄' }
        ],
        data: [
          { name: '张三', age: 25 },
          { name: '李四', age: 30 }
        ]
      }
    })
    
    expect(wrapper.find('table').exists()).toBe(true)
    expect(wrapper.findAll('th').length).toBe(2)
    expect(wrapper.findAll('tr').length).toBe(3) // 标题 + 2行数据
    expect(wrapper.findAll('td').length).toBe(4) // 2行 * 2列
  })
  
  it('emits row-click event when a row is clicked', async () => {
    const wrapper = mount(DataTable, {
      props: {
        columns: [{ key: 'name', label: '姓名' }],
        data: [{ name: '张三' }],
        rowClickable: true
      }
    })
    
    await wrapper.find('tr:not(:first-child)').trigger('click')
    expect(wrapper.emitted('row-click')).toBeTruthy()
    expect(wrapper.emitted('row-click')[0][0]).toEqual({ name: '张三' })
  })
})
```

### 8.2 集成测试

使用 Cypress 进行前端集成测试：

```javascript
// cypress/integration/subscription.spec.js
describe('Subscription Module', () => {
  before(() => {
    cy.login('<EMAIL>', 'password')
  })
  
  it('shows available subscription plans', () => {
    cy.visit('/subscription/plans')
    cy.contains('h2', '选择订阅计划')
    cy.get('.plan-card').should('have.length.at.least', 3)
    cy.contains('成绩分析版').should('be.visible')
    cy.contains('工资管理版').should('be.visible')
    cy.contains('完整版').should('be.visible')
  })
  
  it('redirects to subscription page when accessing restricted module', () => {
    // 假设用户没有成绩模块的访问权限
    cy.visit('/scores')
    cy.url().should('include', '/subscription/required')
    cy.contains('访问受限').should('be.visible')
    cy.contains('您当前的订阅计划不包含对此功能的访问权限').should('be.visible')
  })
  
  it('allows access to subscribed modules', () => {
    // 模拟用户拥有工资模块访问权限
    cy.intercept('GET', '/api/subscription/status', {
      subscription: {
        status: 'active',
        price: 'price_salary'
      },
      availableModules: ['core', 'salary'],
      modules: {
        core: { name: '核心功能' },
        score: { name: '成绩分析' },
        salary: { name: '工资管理' }
      }
    })
    
    cy.visit('/salary')
    cy.url().should('include', '/salary')
    cy.contains('工资概览').should('be.visible')
  })
})
```

## 9. 性能优化

### 9.1 组件懒加载

使用 Vue Router 的动态导入实现组件懒加载：

```typescript
// 延迟加载大型组件
const ScoreAnalysisDashboard = () => import('@/pages/score/analysis-dashboard.vue')
const SalaryReportGenerator = () => import('@/pages/salary/report-generator.vue')
```

### 9.2 虚拟列表

处理大量数据展示时使用虚拟滚动：

```vue
<!-- resources/js/components/common/virtual-list.vue -->
<template>
  <div ref="containerRef" class="virtual-list-container">
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <div 
        v-for="item in visibleItems" 
        :key="item.index"
        :style="{
          position: 'absolute',
          top: item.offset + 'px',
          width: '100%'
        }"
      >
        <slot :item="item.data" :index="item.index" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useScroll } from '@vueuse/core'

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  itemHeight: {
    type: Number,
    default: 50
  },
  buffer: {
    type: Number,
    default: 5 // 上下缓冲区的项目数
  }
})

const containerRef = ref(null)
const { y: scrollTop } = useScroll(containerRef)

// 计算总高度
const totalHeight = computed(() => props.items.length * props.itemHeight)

// 计算可见项目
const visibleItems = computed(() => {
  if (!containerRef.value) return []
  
  const containerHeight = containerRef.value.clientHeight
  const startIndex = Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer)
  const endIndex = Math.min(
    props.items.length - 1,
    Math.ceil((scrollTop.value + containerHeight) / props.itemHeight) + props.buffer
  )
  
  const items = []
  for (let i = startIndex; i <= endIndex; i++) {
    items.push({
      data: props.items[i],
      index: i,
      offset: i * props.itemHeight
    })
  }
  
  return items
})
</script>

<style>
.virtual-list-container {
  overflow-y: auto;
  height: 100%;
}
</style>
```

### 9.3 数据缓存

使用 Pinia 持久化和缓存频繁访问的数据：

```typescript
// resources/js/stores/cache-module.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useCacheStore = defineStore('cache', () => {
  const cache = ref(new Map())
  const expiry = ref(new Map())
  
  function set(key, value, ttl = 5 * 60 * 1000) { // 默认5分钟
    cache.value.set(key, value)
    expiry.value.set(key, Date.now() + ttl)
  }
  
  function get(key) {
    if (!has(key)) return null
    return cache.value.get(key)
  }
  
  function has(key) {
    if (!cache.value.has(key)) return false
    
    const expiryTime = expiry.value.get(key)
    if (expiryTime && expiryTime < Date.now()) {
      remove(key)
      return false
    }
    
    return true
  }
  
  function remove(key) {
    cache.value.delete(key)
    expiry.value.delete(key)
  }
  
  function clear() {
    cache.value.clear()
    expiry.value.clear()
  }
  
  // 定期清理过期缓存
  function cleanExpired() {
    const now = Date.now()
    expiry.value.forEach((expiryTime, key) => {
      if (expiryTime < now) {
        remove(key)
      }
    })
  }
  
  // 每分钟清理一次过期缓存
  setInterval(cleanExpired, 60 * 1000)
  
  return {
    set,
    get,
    has,
    remove,
    clear
  }
})
```

## 10. 主题定制

### 10.1 主题配置

通过 Tailwind CSS 实现主题定制：

```typescript
// tailwind.config.js
const { fontFamily } = require('tailwindcss/defaultTheme')

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './resources/views/**/*.blade.php',
    './resources/js/**/*.vue',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: ['Inter var', ...fontFamily.sans],
      },
    },
  },
}
```

### 10.2 组件样式变体

使用 class-variance-authority 创建组件变体：

```typescript
// resources/js/components/ui/button/index.ts
import { cva } from 'class-variance-authority'

export const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:opacity-50 disabled:pointer-events-none',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)
```

## 11. 总结

EduSaaS 前端组件库采用现代化的组件设计理念，基于 shadcn-vue 和 Vue 3 Composition API 构建，提供了统一、可扩展的用户界面。通过模块化的设计，成绩分析和工资管理两个核心功能可以根据订阅状态动态启用，同时共享通用组件和样式。

前端架构充分利用了Vue 3生态系统的优势，结合Tailwind CSS和TypeScript，提供了类型安全、响应式和高性能的用户体验。从基础UI组件到业务特定功能，每一层都经过精心设计，确保代码的可维护性和可扩展性。

组件库的实现不仅满足了当前系统的需求，还为未来功能扩展和模块添加提供了坚实的基础。通过Pinia状态管理和Vue Router的紧密集成，系统能够根据用户的订阅状态智能地控制UI元素和路由访问，提供流畅的用户体验。