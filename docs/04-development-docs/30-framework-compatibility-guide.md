# 框架兼容性开发指南

## 概述
教学事务管理系统基于 scoreDB25/saas 框架（tenancyforlaravel.com/saas-boilerplate v4 内部版本）构建。该框架会定期更新，为确保系统能够顺利升级到新版本框架，本文档制定了严格的开发规范和兼容性要求。

## 核心原则

### 1. 框架文件不可修改原则
- **绝对禁止**：直接修改 saas_boilerplate 目录中的任何文件
- **原因**：saas_boilerplate 是框架源码，会定期更新覆盖
- **替代方案**：通过扩展、继承、配置等方式实现功能定制

### 2. 解耦设计原则
- **业务逻辑与框架分离**：所有教学管理相关的业务逻辑必须与框架核心功能解耦
- **使用框架标准接口**：通过框架提供的标准接口和扩展点进行开发
- **避免深度耦合**：不依赖框架内部实现细节

### 3. 配置优先原则
- **优先使用配置**：能通过配置解决的问题，不编写代码
- **环境变量控制**：使用环境变量控制功能开关和参数
- **配置文件扩展**：通过配置文件扩展框架功能

## 目录结构规范

### 允许修改的目录
```
saas/
├── app/
│   ├── Models/                    # 可扩展模型
│   ├── Http/Controllers/          # 可添加控制器
│   ├── Http/Middleware/           # 可添加中间件
│   ├── Services/                  # 可添加服务类
│   └── Modules/                   # 教学管理模块（新增）
├── config/                        # 可修改配置文件
├── database/
│   ├── migrations/tenant/         # 可添加租户迁移
│   └── seeders/                   # 可添加种子文件
├── resources/
│   ├── js/                        # 可修改前端代码
│   ├── css/                       # 可修改样式
│   └── views/                     # 可添加视图文件
├── routes/
│   └── modules/                   # 模块路由（新增）
└── storage/                       # 可使用存储目录
```

### 禁止修改的目录
```
saas_boilerplate/                  # 框架源码，绝对禁止修改
```

## 开发规范

### 1. 模型扩展规范
```php
// ✅ 正确：继承框架模型
namespace App\Models;

use App\Models\Tenant as BaseTenant;
use App\Traits\HasEducationData;

class Tenant extends BaseTenant
{
    use HasEducationData;
    
    // 添加教学管理相关的方法
    public function getStudentCount()
    {
        return $this->run(function () {
            return \App\Models\Student::count();
        });
    }
}

// ❌ 错误：直接修改框架模型文件
// 不要修改 saas_boilerplate 中的任何模型文件
```

### 2. 控制器开发规范
```php
// ✅ 正确：创建新的控制器
namespace App\Http\Controllers\Tenant\Score;

use App\Http\Controllers\Controller;
use App\Services\ScoreAnalysisService;

class ScoreAnalysisController extends Controller
{
    public function __construct(
        private ScoreAnalysisService $scoreService
    ) {
        // 使用框架的中间件
        $this->middleware(['auth', 'tenant']);
    }
    
    public function index()
    {
        // 使用框架的权限检查
        $this->authorize('view-scores');
        
        return inertia('Score/Analysis/Index');
    }
}
```

### 3. 中间件扩展规范
```php
// ✅ 正确：创建新的中间件
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class EnsureModuleAccess
{
    public function handle(Request $request, Closure $next, string $module)
    {
        // 使用框架的租户功能
        $tenant = tenant();
        
        // 检查模块订阅状态
        if (!$this->hasModuleAccess($tenant, $module)) {
            return redirect()->route('subscription.required');
        }
        
        return $next($request);
    }
    
    private function hasModuleAccess($tenant, $module): bool
    {
        // 使用框架的订阅检查
        return $tenant->subscribed('default') && 
               in_array($module, $tenant->subscription('default')->modules ?? []);
    }
}
```

### 4. 服务类开发规范
```php
// ✅ 正确：创建独立的服务类
namespace App\Services;

use App\Models\Score;
use App\Models\Student;

class ScoreAnalysisService
{
    public function getClassAnalysis(int $classId): array
    {
        // 业务逻辑与框架解耦
        $scores = Score::where('class_id', $classId)->get();
        
        return [
            'average' => $scores->avg('score'),
            'highest' => $scores->max('score'),
            'lowest' => $scores->min('score'),
            'distribution' => $this->calculateDistribution($scores),
        ];
    }
    
    private function calculateDistribution($scores): array
    {
        // 纯业务逻辑，不依赖框架特定功能
        // ...
    }
}
```

## 配置扩展规范

### 1. 模块配置
```php
// config/modules.php
return [
    'score' => [
        'enabled' => env('SCORE_MODULE_ENABLED', true),
        'features' => [
            'analysis' => env('SCORE_ANALYSIS_ENABLED', true),
            'export' => env('SCORE_EXPORT_ENABLED', true),
        ],
    ],
    'salary' => [
        'enabled' => env('SALARY_MODULE_ENABLED', false),
        'features' => [
            'calculation' => env('SALARY_CALCULATION_ENABLED', true),
            'reports' => env('SALARY_REPORTS_ENABLED', true),
        ],
    ],
];
```

### 2. 订阅计划扩展
```php
// config/saas.php 扩展
'plan_modules' => [
    'price_basic' => [
        'modules' => ['core'],
        'features' => ['basic_user_management'],
    ],
    'price_score' => [
        'modules' => ['core', 'score'],
        'features' => ['basic_user_management', 'score_analysis'],
    ],
    'price_complete' => [
        'modules' => ['core', 'score', 'salary'],
        'features' => ['basic_user_management', 'score_analysis', 'salary_management'],
    ],
],
```

## 数据库规范

### 1. 迁移文件规范
```php
// ✅ 正确：在 tenant 目录下创建迁移
// database/migrations/tenant/2024_01_01_000001_create_scores_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('scores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('exam_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->decimal('score', 5, 2);
            $table->timestamps();
            
            // 使用框架标准的索引命名
            $table->index(['exam_id', 'subject_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('scores');
    }
};
```

### 2. 模型关系规范
```php
// ✅ 正确：使用框架标准的关系定义
class Score extends Model
{
    protected $fillable = [
        'student_id', 'exam_id', 'subject_id', 'score'
    ];
    
    // 使用框架标准的关系方法
    public function student()
    {
        return $this->belongsTo(Student::class);
    }
    
    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }
    
    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }
}
```

## 前端开发规范

### 1. 组件开发规范
```vue
<!-- ✅ 正确：使用框架的布局和组件 -->
<template>
    <AppLayout title="成绩分析">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                成绩分析
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- 使用 shadcn-vue 组件 -->
                <Card>
                    <CardHeader>
                        <CardTitle>班级成绩概览</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ScoreChart :data="scores" />
                    </CardContent>
                </Card>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import { Card, CardHeader, CardTitle, CardContent } from '@/Components/ui/card'
import ScoreChart from '@/Components/Score/ScoreChart.vue'

// 使用框架的数据获取方式
defineProps({
    scores: Array
})
</script>
```

### 2. 路由注册规范
```php
// routes/modules/score.php
use App\Http\Controllers\Tenant\Score\ScoreController;

// ✅ 正确：使用框架的路由组织方式
Route::middleware(['web', 'auth', 'tenant'])
    ->prefix('score')
    ->name('score.')
    ->group(function () {
        Route::get('/', [ScoreController::class, 'index'])->name('index');
        Route::get('/analysis', [ScoreController::class, 'analysis'])->name('analysis');
        
        // API 路由
        Route::prefix('api')->name('api.')->group(function () {
            Route::apiResource('scores', ScoreController::class);
        });
    });
```

## 升级兼容性检查

### 1. 升级前检查清单
- [ ] 确认没有修改 saas_boilerplate 目录中的文件
- [ ] 检查自定义代码是否使用了框架的私有API
- [ ] 验证数据库迁移的兼容性
- [ ] 测试所有自定义功能的正常运行

### 2. 升级流程
```bash
# 1. 备份当前代码
git add .
git commit -m "Backup before framework upgrade"

# 2. 更新框架代码
# (由框架维护者提供具体步骤)

# 3. 运行兼容性测试
php artisan test --group=compatibility

# 4. 检查配置文件变更
php artisan config:diff

# 5. 更新依赖
composer update
npm update
```

## 最佳实践

### 1. 代码组织
- 按模块组织代码，保持清晰的目录结构
- 使用命名空间避免与框架代码冲突
- 遵循 Laravel 的编码规范

### 2. 测试策略
- 为所有自定义功能编写测试
- 使用框架提供的测试工具
- 定期运行兼容性测试

### 3. 文档维护
- 记录所有对框架的扩展和定制
- 维护升级日志和变更记录
- 定期更新开发文档

## 总结
严格遵循本指南的规范，可以确保教学事务管理系统与 scoreDB25/saas 框架保持良好的兼容性，为将来的框架升级奠定坚实基础。开发团队应将框架兼容性作为首要考虑因素，在功能实现和代码质量之间找到最佳平衡点。
