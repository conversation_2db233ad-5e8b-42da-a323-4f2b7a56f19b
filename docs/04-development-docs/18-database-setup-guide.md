# 数据库设计与开发环境配置指南

## 1. 数据库设计评估

教学事务管理系统基于 scoreDB25/saas 框架构建，采用 PostgreSQL 17 数据库，数据库结构设计符合教育事务管理系统的需求，具有良好的模块化和可扩展性：

### 1.1 基础教育表设计

- **组织层次结构**：部门 -> 教师 -> 年级 -> 班级 -> 学生
- **实体间关系**：定义合理，使用了正确的外键约束
- **核心表**：
  - `departments`：部门信息
  - `teachers`：教师信息
  - `grades`：年级信息
  - `classes`：班级信息
  - `students`：学生信息
  - `subjects`：科目信息

### 1.2 成绩分析模块

- **支持各类考试和成绩记录**
- **核心表**：
  - `exams`：考试信息
  - `exam_subjects`：考试科目关联
  - `scores`：学生成绩记录
  - `score_analytics`：成绩分析统计
  - `exam_thresholds`：考试分数阈值
  - `total_scores`：总分汇总
- **功能特点**：
  - 支持多维度的成绩分析
  - 班级和年级排名
  - 自定义阈值管理
  - 成绩分析报表

### 1.3 工资管理模块

- **支持复杂的薪资结构和绩效管理**
- **核心表**：
  - `employees`：员工信息
  - `salary_components`：薪资组成部分
  - `employee_salary_components`：员工与薪资组成关联
  - `salary_calculations`：薪资计算记录
  - `payslips`：工资单
- **功能特点**：
  - 支持自定义薪资组成
  - 灵活的计算公式
  - 工资单生成
  - 审批流程

### 1.4 RBAC权限系统

- **基于角色的访问控制**
- **核心表**：
  - `roles`：角色定义
  - `permissions`：权限定义
  - `role_permissions`：角色权限关联
  - `user_roles`：用户角色关联
- **模块访问控制**：
  - 根据租户订阅分配模块权限
  - 每个权限关联到特定模块

### 1.5 多租户数据库结构

- **中央数据库**：管理租户、域名、订阅信息
- **租户数据库**：每个租户独立数据库实例

## 2. 本地开发环境配置指南

以下是在全新系统上设置项目环境的详细步骤。

### 2.1 环境准备

```bash
# 克隆仓库
git clone [项目地址] scoreDB25
cd scoreDB25

# 进入saas目录
cd saas

# 复制环境配置文件
cp .env.example .env

# 安装依赖
composer install
npm install
```

### 2.2 DDEV初始化配置

```bash
# 初始化ddev项目
cd /Users/<USER>/Code/scoreDB25/saas
ddev config --project-type=laravel --docroot=public --php-version=8.4 --project-name=scoredb25 --database=postgres:17

# 启动DDEV环境
ddev start

# 检查状态
ddev status
```

确保看到类似以下输出：
```
PROJECT     TYPE     PATH                        URL(s)                              STATUS
scoredb25   laravel  /Users/<USER>/Code/scoreDB25   https://scoredb25.ddev.site         running
                                                 https://hiddenadmin.scoredb25.ddev.site
                                                 https://default.scoredb25.ddev.site
                                                 https://test.scoredb25.ddev.site
```

### 2.3 多域名配置

更新 `.ddev/config.yaml` 文件以支持多域名：

```yaml
name: scoredb25
type: laravel
docroot: public
php_version: "8.4"
database:
  type: postgres
  version: "17"

additional_hostnames:
  - hiddenadmin.scoredb25
  - "*.scoredb25"  # 支持所有子域名通配符

additional_fqdns:
  - scoredb25.ddev.site
  - www.scoredb25.ddev.site
  - hiddenadmin.scoredb25.ddev.site
  - default.scoredb25.ddev.site
  - test.scoredb25.ddev.site
```

### 2.3 数据库初始化

```bash
# 生成应用密钥
ddev exec php artisan key:generate

# 运行中央系统迁移
ddev exec php artisan migrate

# 为admin管理员创建一个用户
ddev exec php artisan tinker
```

在tinker控制台中，执行:

```php
use App\Models\Admin;
Admin::create([
    'name' => 'Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'email_verified_at' => now()
]);
exit
```

### 2.4 创建租户和租户数据库

```bash
# 进入tinker控制台
ddev exec php artisan tinker
```

在tinker中执行:

```php
// 创建一个租户
$tenant = App\Models\Tenant::create([
    'email' => '<EMAIL>'
]);

// 创建租户子域名
$tenant->createDomain('school1');

// 确认租户创建成功
App\Models\Tenant::all();
exit
```

### 2.5 前端资源编译

```bash
# 开发环境
ddev exec npm run dev

# 或生产环境
ddev exec npm run build
```

### 2.6 访问系统

- **主域名（宣传和注册）**: https://scoredb25.ddev.site 或 https://www.scoredb25.ddev.site
  - 用于系统宣传、用户注册、订阅购买

- **系统管理后台**: https://hiddenadmin.scoredb25.ddev.site/admin/login
  - 使用创建的管理员邮箱和密码登录

- **默认租户**: https://default.scoredb25.ddev.site/login
  - 邮箱: <EMAIL>
  - 密码: password

- **测试租户**: https://test.scoredb25.ddev.site/login
  - 邮箱: <EMAIL>
  - 密码: password
  - 包含大量模拟数据

### 2.7 模块访问测试

根据saas.php配置文件，系统创建了四种订阅计划：
- `price_basic` - 基础版（仅核心功能）
- `price_score` - 成绩分析版（包含成绩分析模块）
- `price_salary` - 工资管理版（包含工资管理模块）
- `price_complete` - 完整版（包含所有模块）

要更新租户的订阅计划，可以使用以下命令：

```bash
ddev exec php artisan tinker

// 为租户设置指定计划的订阅
$tenant = App\Models\Tenant::first();
$tenant->createOrGetStripeCustomer();
$tenant->subscriptions()->create([
    'name' => 'default',
    'stripe_price' => 'price_score', // 选择订阅计划
    'stripe_id' => 'sub_' . Str::random(10),
    'stripe_status' => 'active',
    'module_access' => ['core', 'score'],
    'quantity' => 1
]);

// 检查订阅状态
$tenant->subscribed('default');
exit
```

## 3. 模块化数据库迁移架构

为了更好地支持系统的模块化设计，我们对数据库迁移文件进行了优化组织，使其与模块功能一一对应：

### 3.1 迁移文件目录结构

```
database/migrations/         # 中央系统迁移
  ├── 0001_*.php             # 管理员表等基础迁移
  ├── 201*_*.php             # 租户与域名表
  └── 202*_*.php             # 订阅相关表

database/migrations/tenant/  # 租户系统迁移
  ├── core/                  # 核心模块迁移
  │   ├── 0001_*.php         # 用户表
  │   └── 0002_*.php         # 角色权限表
  ├── score/                 # 成绩分析模块迁移
  │   ├── 0001_*.php         # 考试相关表
  │   └── 0002_*.php         # 成绩相关表
  └── salary/                # 工资模块迁移
      ├── 0001_*.php         # 员工相关表
      └── 0002_*.php         # 薪资相关表
```

### 3.2 模块依赖关系

- **Core模块**：系统基础表，所有租户必需
  - 用户管理 (users)
  - 角色权限 (roles, permissions)
  - 组织结构 (departments, etc.)

- **Score模块**：成绩分析相关表，依赖Core模块
  - 考试管理 (exams, exam_subjects)
  - 成绩记录 (scores)
  - 统计分析 (score_analytics)

- **Salary模块**：工资管理相关表，依赖Core模块
  - 员工管理 (employees)
  - 薪资结构 (salary_components)
  - 薪资计算 (salary_calculations)

### 3.3 模块化迁移管理

系统实现了基于订阅的模块化迁移加载机制：

1. **按需加载**：根据租户订阅的模块有选择地加载迁移文件
2. **条件迁移**：提供模块化迁移命令，可针对特定模块进行迁移
3. **版本独立**：各模块的迁移可以独立演进，不影响其他模块

### 3.4 模块化种子数据

与迁移文件对应，系统也按模块组织种子文件：

- **CoreSeeder.php**：核心模块基础数据，包括角色、权限等
- **ScoreModuleSeeder.php**：成绩模块示例数据
- **SalaryModuleSeeder.php**：工资模块示例数据

这种模块化设计使系统在以下方面获益：

- **清晰的代码组织**：迁移文件按功能模块分组，便于维护
- **优化的数据库结构**：租户数据库只包含其订阅模块所需的表
- **简化的开发流程**：开发人员可以专注于特定模块的开发
- **灵活的功能扩展**：新增模块只需添加对应的迁移文件和种子数据

## 4. 总结

数据库结构设计完善，很好地支持了教育事务管理系统的需求，特别是成绩分析和工资管理两个核心模块。系统采用的模块化设计和多租户架构使其具备良好的可扩展性和灵活性，能够根据用户需求进行定制和扫展。

对于开发者和管理员，本文档提供了从环境搭建到系统配置的完整指南，可以快速启动并测试系统的各项功能。