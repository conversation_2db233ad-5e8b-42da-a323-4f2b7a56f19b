#!/bin/bash

echo "🚀 初始化SaaS多租户演示数据..."

# 检查是否需要强制重置
FORCE_RESET=${1:-""}

if [ "$FORCE_RESET" = "--force" ]; then
    echo "⚠️  强制重置模式：将删除所有现有数据"

    # 删除所有租户数据库
    echo "🗑️ 删除所有租户数据库..."

    # 获取所有租户数据库名称并逐个删除
    TENANT_DBS=$(ddev exec -s db psql -t -c "SELECT datname FROM pg_database WHERE datname LIKE 'tenant%' OR datname LIKE 'test_%tenant%';" 2>/dev/null | tr -d ' ')

    if [ ! -z "$TENANT_DBS" ]; then
        echo "$TENANT_DBS" | while read -r db_name; do
            if [ ! -z "$db_name" ]; then
                echo "删除数据库: $db_name"
                ddev exec -s db psql -c "DROP DATABASE IF EXISTS \"$db_name\";" 2>/dev/null || echo "无法删除 $db_name"
            fi
        done
    else
        echo "没有找到租户数据库"
    fi

    # 清理中央数据库
    echo "🧹 重置中央数据库..."
    ddev exec php artisan migrate:fresh --seed
else
    echo "🔄 智能更新模式：保留现有数据，仅更新必要部分"

    # 检查中央数据库是否已初始化
    echo "📋 检查中央数据库状态..."
    TABLES_COUNT=$(ddev exec -s db psql -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ' || echo "0")

    if [ "$TABLES_COUNT" = "0" ] || [ -z "$TABLES_COUNT" ]; then
        echo "🏗️ 中央数据库未初始化，运行迁移..."
        ddev exec php artisan migrate --seed
    else
        echo "✅ 中央数据库已存在，运行种子数据更新..."
        ddev exec php artisan db:seed
    fi
fi

# 为每个租户运行迁移（如果需要）
echo "🏗️ 检查并运行租户迁移..."
ddev exec php artisan tenants:migrate

# 为每个租户运行种子数据
echo "🌱 为租户运行种子数据（这可能需要几分钟时间）..."
echo "📊 正在生成大量演示数据："
echo "   - 3个年级 × 6个班级 = 18个班级"
echo "   - 每班40-45名学生 ≈ 720-810名学生"
echo "   - 45名教师（管理层6名 + 各科目教师39名）"
echo "   - 8种考试类型 × 3个年级 = 24场考试"
echo "   - 大量成绩数据（每场考试约4000-5000条记录）"
echo ""
ddev exec php artisan tenants:seed

# 验证设置
echo "📊 验证设置..."
echo "中央数据库表："
ddev exec -s db psql -c "\dt" 2>/dev/null | head -10 || echo "无法连接到数据库"

echo ""
echo "租户数据库："
ddev exec -s db psql -l 2>/dev/null | grep tenant || echo "无法列出租户数据库"

echo "✅ 演示数据重置完成！"
echo ""
echo "🌐 访问地址："
echo "- 默认租户: https://default.scoredb25.ddev.site"
echo "- 测试租户: https://test.scoredb25.ddev.site"
echo "- 管理后台: https://hiddenadmin.scoredb25.ddev.site"
echo ""
echo "🔑 默认登录信息："
echo "- 用户名: admin"
echo "- 密码: password"
