#!/bin/bash

echo "🔍 调试租户创建过程..."

# 删除现有的default租户数据库
echo "1. 删除现有租户数据库..."
ddev exec -s db psql -c "DROP DATABASE IF EXISTS tenantdefault;" 2>/dev/null || echo "数据库不存在或已删除"

echo "2. 测试中央数据库迁移速度..."
time ddev exec php artisan migrate:status

echo "3. 测试租户迁移速度（空租户）..."
time ddev exec php artisan tenants:migrate --tenants=nonexistent

echo "4. 检查租户迁移路径配置..."
ddev exec php artisan config:show tenancy.migration_parameters

echo "5. 列出租户迁移文件..."
echo "Core migrations:"
ls -la database/migrations/tenant/core/ | wc -l
echo "Score migrations:"
ls -la database/migrations/tenant/score/ | wc -l
echo "Salary migrations:"
ls -la database/migrations/tenant/salary/ | wc -l

echo "6. 测试单个租户创建（通过seeder）..."
echo "开始时间: $(date)"
time ddev exec php artisan db:seed --class=TenantSeeder
echo "结束时间: $(date)"

echo "7. 检查创建的租户数据库..."
ddev exec -s db psql -l | grep tenant | tail -5

echo "✅ 调试完成"
