#!/bin/bash

echo "🔍 验证演示数据完整性..."

# 检查租户数据库
echo "📊 检查租户数据统计："

# 获取租户数据库列表并保存到临时文件
TEMP_FILE=$(mktemp)
ddev exec -s db psql -t -c "SELECT datname FROM pg_database WHERE datname LIKE 'tenant%';" 2>/dev/null | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//' | grep -v '^$' > "$TEMP_FILE"

if [ ! -s "$TEMP_FILE" ]; then
    echo "❌ 没有找到租户数据库"
    rm -f "$TEMP_FILE"
    exit 1
fi

echo "找到租户数据库："
cat "$TEMP_FILE"
echo ""

# 对每个租户数据库进行检查
while IFS= read -r db_name <&3; do
    if [ ! -z "$db_name" ] && [ "$db_name" != "" ]; then
        echo "🏫 检查数据库: $db_name"

        # 检查各表的记录数
        echo "  📋 数据统计："

        # 部门数量
        DEPT_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM departments;" 2>/dev/null | tr -d ' ')
        echo "    部门: $DEPT_COUNT 个"

        # 年级数量
        GRADE_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM grades;" 2>/dev/null | tr -d ' ')
        echo "    年级: $GRADE_COUNT 个"

        # 班级数量
        CLASS_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM classes;" 2>/dev/null | tr -d ' ')
        echo "    班级: $CLASS_COUNT 个"

        # 科目数量
        SUBJECT_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM subjects;" 2>/dev/null | tr -d ' ')
        echo "    科目: $SUBJECT_COUNT 个"

        # 教师数量
        TEACHER_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM teachers;" 2>/dev/null | tr -d ' ')
        echo "    教师: $TEACHER_COUNT 名"

        # 学生数量
        STUDENT_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM students;" 2>/dev/null | tr -d ' ')
        echo "    学生: $STUDENT_COUNT 名"

        # 用户数量
        USER_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | tr -d ' ')
        echo "    用户: $USER_COUNT 个"

        # 角色数量
        ROLE_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM roles;" 2>/dev/null | tr -d ' ')
        echo "    角色: $ROLE_COUNT 个"

        # 权限数量
        PERMISSION_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM permissions;" 2>/dev/null | tr -d ' ')
        echo "    权限: $PERMISSION_COUNT 个"

        # 考试数量
        EXAM_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM exams;" 2>/dev/null | tr -d ' ')
        echo "    考试: $EXAM_COUNT 场"

        # 成绩数量
        SCORE_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM scores;" 2>/dev/null | tr -d ' ')
        echo "    成绩记录: $SCORE_COUNT 条"

        echo ""

        # 检查数据完整性
        echo "  🔍 数据完整性检查："

        # 检查是否有管理员用户
        ADMIN_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM users u JOIN user_roles ur ON u.id = ur.user_id JOIN roles r ON ur.role_id = r.id WHERE r.name = 'super_admin';" 2>/dev/null | tr -d ' ')
        if [ "$ADMIN_COUNT" -gt 0 ]; then
            echo "    ✅ 管理员用户: $ADMIN_COUNT 个"
        else
            echo "    ❌ 没有管理员用户"
        fi

        # 检查是否有班主任分配
        CLASS_TEACHER_COUNT=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT COUNT(*) FROM classes WHERE class_teacher_id IS NOT NULL;" 2>/dev/null | tr -d ' ')
        echo "    ✅ 已分配班主任的班级: $CLASS_TEACHER_COUNT 个"

        # 检查成绩数据分布
        if [ "$SCORE_COUNT" -gt 0 ]; then
            AVG_SCORE=$(ddev exec -s db psql -d "$db_name" -t -c "SELECT ROUND(AVG(score), 2) FROM scores WHERE is_absent = false;" 2>/dev/null | tr -d ' ')
            echo "    📊 平均成绩: $AVG_SCORE 分"
        fi

        echo "  ----------------------------------------"
        echo ""
    fi
done 3< "$TEMP_FILE"

# 清理临时文件
rm -f "$TEMP_FILE"

echo "✅ 数据验证完成！"
echo ""
echo "🌐 可以访问以下地址测试系统："
echo "- 默认租户: https://default.scoredb25.ddev.site"
echo "- 测试租户: https://test.scoredb25.ddev.site"
echo "- 管理后台: https://hiddenadmin.scoredb25.ddev.site"
echo ""
echo "🔑 默认登录信息："
echo "- 用户名: admin"
echo "- 密码: password"
