<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>"  class="<?php echo \Illuminate\Support\Arr::toCssClasses(['dark' => ($appearance ?? 'system') == 'dark']); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        
        <script>
            // Enhanced URL fixing for DDEV Vite integration
            function fixViteUrls() {
                // Fix existing elements
                document.querySelectorAll('script[src*=":5173"], link[href*=":5173"]').forEach(element => {
                    const attr = element.hasAttribute('src') ? 'src' : 'href';
                    const originalUrl = element.getAttribute(attr);
                    let fixedUrl = originalUrl;

                    // Fix /:5173/ pattern (common issue)
                    fixedUrl = fixedUrl.replace('/:5173/', ':5173/');

                    // Fix double slashes in paths
                    fixedUrl = fixedUrl.replace(/([^:])\/\/+/g, '$1/');

                    // Ensure proper protocol for external resources
                    if (fixedUrl.startsWith('//')) {
                        fixedUrl = window.location.protocol + fixedUrl;
                    }

                    if (fixedUrl !== originalUrl) {
                        console.log(`Fixed Vite URL: ${originalUrl} -> ${fixedUrl}`);
                        element.setAttribute(attr, fixedUrl);
                    }
                });

                // Create a MutationObserver to watch for new elements
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes) {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1 && (node.tagName === 'SCRIPT' || node.tagName === 'LINK')) {
                                    const attr = node.tagName === 'SCRIPT' ? 'src' : 'href';
                                    const url = node.getAttribute(attr);
                                    if (url && url.includes('/:5173/')) {
                                        const fixedUrl = url.replace('/:5173/', ':5173/');
                                        console.log(`Fixed dynamic Vite URL: ${url} -> ${fixedUrl}`);
                                        node.setAttribute(attr, fixedUrl);
                                    }
                                }
                            });
                        }
                    });
                });

                // Start observing the document with the configured parameters
                observer.observe(document, { childList: true, subtree: true });
            }

            // Run immediately and again when DOM is ready
            fixViteUrls();
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixViteUrls);
            }
        </script>

        
        <script>
            (function() {
                const appearance = '<?php echo e($appearance ?? "system"); ?>';

                if (appearance === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (prefersDark) {
                        document.documentElement.classList.add('dark');
                    }
                }
            })();
        </script>

        
        <style>
            html {
                background-color: oklch(1 0 0);
            }

            html.dark {
                background-color: oklch(0.145 0 0);
            }
        </style>

        <title inertia><?php echo e(config('app.name', 'Laravel')); ?></title>

        <link rel="icon" href="/favicon.ico" sizes="any">
        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.ts', "resources/js/pages/{$page['component']}.vue"]); ?>
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->head; } ?>
    </head>
    <body class="font-sans antialiased">
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->body; } else { ?><div id="app" data-page="<?php echo e(json_encode($page)); ?>"></div><?php } ?>
    </body>
</html>
<?php /**PATH /var/www/html/resources/views/app.blade.php ENDPATH**/ ?>