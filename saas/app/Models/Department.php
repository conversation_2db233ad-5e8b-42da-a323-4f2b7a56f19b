<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'head_id',
        'parent_id',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the teachers in this department.
     */
    public function teachers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Teacher::class);
    }

    /**
     * Get the head of the department.
     */
    public function head()
    {
        return $this->belongsTo(Teacher::class, 'head_id');
    }

    /**
     * Get the parent department.
     */
    public function parent()
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * Get the child departments.
     */
    public function children(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Department::class, 'parent_id');
    }
}
