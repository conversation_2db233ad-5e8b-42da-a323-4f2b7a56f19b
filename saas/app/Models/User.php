<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Exceptions\EmailOccupiedException;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Models\Post;
use App\Models\Role;
use App\Models\Classes;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\Student;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'real_name',
        'name',
        'email',
        'password',
        'is_active',
        'email_verified_at',
        'provider',
        'provider_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var list<string>
     */
    protected $appends = [
        'is_owner',
        'permissions',
    ];

    public static function booted()
    {
        static::updating(function (self $user) {
            if ($user->isOwner()) {
                $tenant = Tenant::firstWhere('email', $user->getOriginal('email'));

                if (Tenant::where('email', $user->email)->where('id', '!=', $tenant->id)->exists()) {
                    throw new EmailOccupiedException;
                }

                // We update the tenant's email when the admin user's email is updated
                // so that the tenant can find his account even after email change.
                $tenant->update($user->only(['email']));
            }
        });
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return list<string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * This is the "organization" owner.
     */
    public function isOwner(): bool
    {
        // We assume the superadmin is the first user in the tenant DB.
        // Feel free to change this logic.
        if (tenant()) {
            return $this->getKey() === User::first()->getKey();
        }

        return false;
    }

    public function getIsOwnerAttribute()
    {
        return $this->isOwner();
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the roles that belong to the user.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->withTimestamps();
    }

    /**
     * Get the permissions that belong to the user through roles.
     */
    public function getPermissionsAttribute(): array
    {
        return $this->roles()
            ->with('permissions')
            ->get()
            ->pluck('permissions')
            ->flatten()
            ->pluck('slug')
            ->unique()
            ->toArray();
    }

    /**
     * Check if the user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions);
    }

    /**
     * Check if the user has a specific role.
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * Get the user's display name (real_name if available, otherwise username or name).
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->real_name ?? $this->username ?? $this->name ?? $this->email;
    }

    /**
     * Get the user's primary role for the education system.
     */
    public function getRole(): string
    {
        // 按优先级返回用户的主要角色
        $roleHierarchy = [
            'principal',
            'academic_director',
            'student_affairs_director',
            'grade_director',
            'class_teacher',
            'subject_leader',
            'subject_teacher',
            'staff',
            'student',
            'parent'
        ];

        foreach ($roleHierarchy as $role) {
            if ($this->hasRole($role)) {
                return $role;
            }
        }

        return 'guest';
    }

    /**
     * Check if user has any of the specified roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        foreach ($roles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get the grades managed by this user (for grade directors).
     */
    public function managedGrades()
    {
        // 这里需要根据实际的数据库结构来实现
        // 假设有一个 user_managed_grades 表
        return $this->belongsToMany(Grade::class, 'user_managed_grades');
    }

    /**
     * Get the classes managed by this user (for class teachers).
     */
    public function managedClasses()
    {
        // 假设有一个 user_managed_classes 表
        return $this->belongsToMany(Classes::class, 'user_managed_classes');
    }

    /**
     * Get the subjects managed by this user (for subject leaders).
     */
    public function managedSubjects()
    {
        // 假设有一个 user_managed_subjects 表
        return $this->belongsToMany(Subject::class, 'user_managed_subjects');
    }

    /**
     * Get the classes this user teaches (for subject teachers).
     */
    public function teachingClasses()
    {
        // 假设有一个 user_teaching_classes 表
        return $this->belongsToMany(Classes::class, 'user_teaching_classes');
    }

    /**
     * Get the subjects this user teaches (for subject teachers).
     */
    public function teachingSubjects()
    {
        // 假设有一个 user_teaching_subjects 表
        return $this->belongsToMany(Subject::class, 'user_teaching_subjects');
    }

    /**
     * Get the children of this user (for parents).
     */
    public function children()
    {
        // 假设有一个 user_children 表
        return $this->belongsToMany(Student::class, 'user_children', 'parent_id', 'student_id');
    }

    /**
     * Check if user can access specific data based on their role and scope.
     */
    public function canAccessData(string $type, $resourceId = null): bool
    {
        $role = $this->getRole();

        switch ($role) {
            case 'principal':
            case 'academic_director':
            case 'student_affairs_director':
                // 全校权限
                return true;

            case 'grade_director':
                // 年级权限
                if ($type === 'grade') {
                    return $this->managedGrades()->where('id', $resourceId)->exists();
                }
                if ($type === 'class') {
                    return $this->managedGrades()
                        ->whereHas('classes', function ($query) use ($resourceId) {
                            $query->where('id', $resourceId);
                        })->exists();
                }
                return false;

            case 'class_teacher':
                // 班级权限
                if ($type === 'class') {
                    return $this->managedClasses()->where('id', $resourceId)->exists();
                }
                return false;

            case 'subject_leader':
                // 学科权限（全校）
                if ($type === 'subject') {
                    return $this->managedSubjects()->where('id', $resourceId)->exists();
                }
                return true; // 学科组长可以查看全校相关学科数据

            case 'subject_teacher':
                // 教学权限
                if ($type === 'class') {
                    return $this->teachingClasses()->where('id', $resourceId)->exists();
                }
                if ($type === 'subject') {
                    return $this->teachingSubjects()->where('id', $resourceId)->exists();
                }
                return false;

            case 'student':
                // 个人权限
                return $type === 'student' && $resourceId === $this->id;

            case 'parent':
                // 子女权限
                return $type === 'student' && $this->children()->where('id', $resourceId)->exists();

            default:
                return false;
        }
    }
}
