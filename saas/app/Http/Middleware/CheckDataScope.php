<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckDataScope
{
    /**
     * Handle an incoming request to check data access scope.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $scope = 'default'): Response
    {
        // Skip check if no tenant context
        if (!tenant()) {
            return $next($request);
        }

        // Skip check if user is not authenticated
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Add user's data scope to request for use in controllers
        $request->merge([
            'user_data_scope' => $this->getUserDataScope($user, $scope),
            'user_role' => $user->getRole(),
        ]);

        return $next($request);
    }

    /**
     * Get user's data access scope based on their role.
     */
    private function getUserDataScope($user, string $scope): array
    {
        $role = $user->getRole();

        switch ($role) {
            case 'principal':
            case 'academic_director':
            case 'student_affairs_director':
                // 全校范围
                return [
                    'type' => 'school',
                    'scope' => 'all',
                    'constraints' => []
                ];

            case 'grade_director':
                // 年级范围
                return [
                    'type' => 'grade',
                    'scope' => 'grade',
                    'constraints' => [
                        'grade_ids' => $user->managedGrades()->pluck('id')->toArray()
                    ]
                ];

            case 'class_teacher':
                // 班级范围
                return [
                    'type' => 'class',
                    'scope' => 'class',
                    'constraints' => [
                        'class_ids' => $user->managedClasses()->pluck('id')->toArray()
                    ]
                ];

            case 'subject_leader':
                // 学科范围（全校）
                return [
                    'type' => 'subject',
                    'scope' => 'subject_school',
                    'constraints' => [
                        'subject_ids' => $user->managedSubjects()->pluck('id')->toArray()
                    ]
                ];

            case 'subject_teacher':
                // 教学范围（所教授的班级和学科）
                return [
                    'type' => 'teaching',
                    'scope' => 'teaching',
                    'constraints' => [
                        'class_ids' => $user->teachingClasses()->pluck('id')->toArray(),
                        'subject_ids' => $user->teachingSubjects()->pluck('id')->toArray()
                    ]
                ];

            case 'staff':
                // 工作人员（根据具体职责）
                if ($scope === 'salary') {
                    // 薪资管理权限
                    return [
                        'type' => 'school',
                        'scope' => 'all',
                        'constraints' => []
                    ];
                }
                // 其他情况基础查看权限
                return [
                    'type' => 'basic',
                    'scope' => 'view_only',
                    'constraints' => []
                ];

            case 'student':
                // 个人范围
                return [
                    'type' => 'personal',
                    'scope' => 'self',
                    'constraints' => [
                        'student_id' => $user->student_id ?? $user->id
                    ]
                ];

            case 'parent':
                // 子女范围
                return [
                    'type' => 'children',
                    'scope' => 'children',
                    'constraints' => [
                        'student_ids' => $user->children()->pluck('id')->toArray()
                    ]
                ];

            default:
                // 默认无权限
                return [
                    'type' => 'none',
                    'scope' => 'none',
                    'constraints' => []
                ];
        }
    }
}
