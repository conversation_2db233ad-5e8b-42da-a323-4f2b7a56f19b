<?php

declare(strict_types=1);

namespace App\Http\Controllers\Central;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WelcomeController extends Controller
{
    /**
     * Display the welcome page for the main domain.
     */
    public function index()
    {
        return Inertia::render('Welcome', [
            'systemName' => config('app.name'),
            'adminDomain' => config('app.admin_domain'),
            'centralDomain' => config('app.central_domain'),
            'features' => [
                [
                    'title' => '成绩分析系统',
                    'description' => '全面的学生成绩管理与分析，支持多维度数据分析和趋势预测',
                    'icon' => 'chart-bar',
                    'module' => 'score'
                ],
                [
                    'title' => '工资管理系统',
                    'description' => '教职工薪资管理，支持复杂薪资结构计算和统计分析',
                    'icon' => 'currency-dollar',
                    'module' => 'salary'
                ],
                [
                    'title' => '多租户架构',
                    'description' => '完全数据隔离的多学校管理，每个学校独立的数据库和域名',
                    'icon' => 'building-office',
                    'module' => 'core'
                ],
                [
                    'title' => '角色权限管理',
                    'description' => '细粒度的角色权限控制，支持校长、教师、学生、家长等多种角色',
                    'icon' => 'user-group',
                    'module' => 'core'
                ]
            ],
            'plans' => [
                [
                    'name' => '基础版',
                    'price' => '免费',
                    'description' => '适合小型学校的基础功能',
                    'features' => [
                        '用户管理',
                        '基础权限控制',
                        '学校信息管理',
                        '班级年级管理'
                    ],
                    'modules' => ['core']
                ],
                [
                    'name' => '成绩分析版',
                    'price' => '¥299/月',
                    'description' => '包含完整的成绩分析功能',
                    'features' => [
                        '基础版所有功能',
                        '考试管理',
                        '成绩录入与分析',
                        '多维度统计报表',
                        '趋势分析预测'
                    ],
                    'modules' => ['core', 'score'],
                    'popular' => true
                ],
                [
                    'name' => '完整版',
                    'price' => '¥499/月',
                    'description' => '包含所有功能模块',
                    'features' => [
                        '成绩分析版所有功能',
                        '教职工管理',
                        '工资结构配置',
                        '薪资计算与统计',
                        '完整报表系统'
                    ],
                    'modules' => ['core', 'score', 'salary']
                ]
            ]
        ]);
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        return Inertia::render('About', [
            'systemName' => config('app.name'),
            'version' => '1.0.0',
            'framework' => 'Laravel 12',
            'database' => 'PostgreSQL 17',
            'frontend' => 'Vue.js 3 + shadcn-vue'
        ]);
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return Inertia::render('Contact', [
            'systemName' => config('app.name'),
            'supportEmail' => 'support@' . config('app.central_domain'),
            'salesEmail' => 'sales@' . config('app.central_domain')
        ]);
    }

    /**
     * Display the pricing page.
     */
    public function pricing()
    {
        return Inertia::render('Pricing', [
            'systemName' => config('app.name'),
            'plans' => [
                [
                    'id' => 'basic',
                    'name' => '基础版',
                    'price' => 0,
                    'billing' => 'month',
                    'description' => '适合小型学校的基础功能',
                    'features' => [
                        '最多100名学生',
                        '基础用户管理',
                        '学校信息管理',
                        '班级年级管理',
                        '基础权限控制'
                    ],
                    'modules' => ['core'],
                    'stripe_price_id' => null
                ],
                [
                    'id' => 'score',
                    'name' => '成绩分析版',
                    'price' => 299,
                    'billing' => 'month',
                    'description' => '包含完整的成绩分析功能',
                    'features' => [
                        '无限学生数量',
                        '基础版所有功能',
                        '考试管理',
                        '成绩录入与分析',
                        '多维度统计报表',
                        '趋势分析预测',
                        '数据导入导出'
                    ],
                    'modules' => ['core', 'score'],
                    'popular' => true,
                    'stripe_price_id' => env('STRIPE_PRICE_SCORE_MONTHLY')
                ],
                [
                    'id' => 'complete',
                    'name' => '完整版',
                    'price' => 499,
                    'billing' => 'month',
                    'description' => '包含所有功能模块',
                    'features' => [
                        '成绩分析版所有功能',
                        '教职工管理',
                        '工资结构配置',
                        '薪资计算与统计',
                        '完整报表系统',
                        '高级数据分析',
                        '优先技术支持'
                    ],
                    'modules' => ['core', 'score', 'salary'],
                    'stripe_price_id' => env('STRIPE_PRICE_COMPLETE_MONTHLY')
                ]
            ]
        ]);
    }

    /**
     * Handle tenant login redirect.
     */
    public function tenantLogin(Request $request)
    {
        $subdomain = $request->input('subdomain');
        
        if (!$subdomain) {
            return redirect()->route('central.login')
                ->with('error', '请输入学校域名');
        }

        // 验证子域名格式
        if (!preg_match('/^[a-zA-Z0-9-]+$/', $subdomain)) {
            return redirect()->route('central.login')
                ->with('error', '域名格式不正确');
        }

        // 构建租户URL
        $tenantUrl = 'https://' . $subdomain . '.' . config('app.central_domain') . '/login';
        
        return redirect()->to($tenantUrl);
    }
}
