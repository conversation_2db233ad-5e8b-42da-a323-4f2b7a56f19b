<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Exam;
use App\Models\Score;
use App\Models\Student;
use App\Models\Classes;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ScoreAnalysisController extends Controller
{
    /**
     * Get exam overview analysis.
     */
    public function getExamOverview(Exam $exam): JsonResponse
    {
        $scores = Score::where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['student.class.grade', 'subject'])
            ->get();

        if ($scores->isEmpty()) {
            return response()->json([
                'message' => '该考试暂无成绩数据'
            ]);
        }

        // 基础统计
        $totalStudents = Score::where('exam_id', $exam->id)->distinct('student_id')->count();
        $participatedStudents = $scores->groupBy('student_id')->count();
        $absentStudents = $totalStudents - $participatedStudents;

        // 科目分析
        $subjectAnalysis = $scores->groupBy('subject_id')->map(function ($subjectScores) {
            $subject = $subjectScores->first()->subject;
            $validScores = $subjectScores->pluck('score');
            
            return [
                'subject_name' => $subject->name,
                'participant_count' => $subjectScores->count(),
                'average_score' => round($validScores->avg(), 2),
                'highest_score' => $validScores->max(),
                'lowest_score' => $validScores->min(),
                'pass_rate' => round($validScores->filter(fn($s) => $s >= $subject->pass_score)->count() / $validScores->count() * 100, 2),
                'excellent_rate' => round($validScores->filter(fn($s) => $s >= 90)->count() / $validScores->count() * 100, 2),
            ];
        })->values();

        // 年级分析
        $gradeAnalysis = $scores->groupBy('student.class.grade_id')->map(function ($gradeScores) {
            $grade = $gradeScores->first()->student->class->grade;
            $studentCount = $gradeScores->groupBy('student_id')->count();
            $totalScore = $gradeScores->groupBy('student_id')->map(fn($studentScores) => $studentScores->sum('score'));
            
            return [
                'grade_name' => $grade->name,
                'student_count' => $studentCount,
                'average_total_score' => round($totalScore->avg(), 2),
                'highest_total_score' => $totalScore->max(),
                'lowest_total_score' => $totalScore->min(),
            ];
        })->values();

        // 班级分析
        $classAnalysis = $scores->groupBy('student.class_id')->map(function ($classScores) {
            $class = $classScores->first()->student->class;
            $studentCount = $classScores->groupBy('student_id')->count();
            $totalScore = $classScores->groupBy('student_id')->map(fn($studentScores) => $studentScores->sum('score'));
            
            return [
                'class_name' => $class->name,
                'grade_name' => $class->grade->name,
                'student_count' => $studentCount,
                'average_total_score' => round($totalScore->avg(), 2),
                'class_rank' => 0, // 将在后面计算
            ];
        })->sortByDesc('average_total_score')->values();

        // 为班级添加排名
        $classAnalysis = $classAnalysis->map(function ($class, $index) {
            $class['class_rank'] = $index + 1;
            return $class;
        });

        $overview = [
            'exam_info' => [
                'name' => $exam->name,
                'type' => $exam->exam_type,
                'start_date' => $exam->start_date,
                'end_date' => $exam->end_date,
            ],
            'participation_stats' => [
                'total_students' => $totalStudents,
                'participated_students' => $participatedStudents,
                'absent_students' => $absentStudents,
                'participation_rate' => round($participatedStudents / $totalStudents * 100, 2),
            ],
            'subject_analysis' => $subjectAnalysis,
            'grade_analysis' => $gradeAnalysis,
            'class_analysis' => $classAnalysis,
        ];

        return response()->json($overview);
    }

    /**
     * Get score trends analysis.
     */
    public function getScoreTrends(Exam $exam): JsonResponse
    {
        // 获取同一学年的历史考试数据进行趋势分析
        $historicalExams = Exam::where('academic_year', $exam->academic_year)
            ->where('start_date', '<=', $exam->start_date)
            ->orderBy('start_date')
            ->get();

        $trends = $historicalExams->map(function ($historicalExam) {
            $scores = Score::where('exam_id', $historicalExam->id)
                ->where('is_absent', false)
                ->with(['subject'])
                ->get();

            if ($scores->isEmpty()) {
                return null;
            }

            // 计算各科目平均分
            $subjectAverages = $scores->groupBy('subject_id')->map(function ($subjectScores) {
                $subject = $subjectScores->first()->subject;
                return [
                    'subject_name' => $subject->name,
                    'average' => round($subjectScores->avg('score'), 2),
                ];
            })->values();

            // 计算总体平均分
            $totalAverage = $scores->groupBy('student_id')->map(function ($studentScores) {
                return $studentScores->sum('score');
            })->avg();

            return [
                'exam_name' => $historicalExam->name,
                'exam_date' => $historicalExam->start_date,
                'total_average' => round($totalAverage, 2),
                'subject_averages' => $subjectAverages,
                'participant_count' => $scores->groupBy('student_id')->count(),
            ];
        })->filter()->values();

        return response()->json([
            'exam_name' => $exam->name,
            'trends' => $trends,
            'analysis' => $this->analyzeTrendDirection($trends),
        ]);
    }

    /**
     * Get score distribution analysis.
     */
    public function getScoreDistribution(Exam $exam): JsonResponse
    {
        $scores = Score::where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['subject'])
            ->get();

        if ($scores->isEmpty()) {
            return response()->json([
                'message' => '该考试暂无成绩数据'
            ]);
        }

        // 总分分布
        $totalScores = $scores->groupBy('student_id')->map(function ($studentScores) {
            return $studentScores->sum('score');
        });

        $totalDistribution = [
            '优秀(90%以上)' => $totalScores->filter(function ($score) use ($exam) {
                $maxPossible = $exam->examSubjects->sum('full_score');
                return $score >= $maxPossible * 0.9;
            })->count(),
            '良好(80-90%)' => $totalScores->filter(function ($score) use ($exam) {
                $maxPossible = $exam->examSubjects->sum('full_score');
                return $score >= $maxPossible * 0.8 && $score < $maxPossible * 0.9;
            })->count(),
            '中等(70-80%)' => $totalScores->filter(function ($score) use ($exam) {
                $maxPossible = $exam->examSubjects->sum('full_score');
                return $score >= $maxPossible * 0.7 && $score < $maxPossible * 0.8;
            })->count(),
            '及格(60-70%)' => $totalScores->filter(function ($score) use ($exam) {
                $maxPossible = $exam->examSubjects->sum('full_score');
                return $score >= $maxPossible * 0.6 && $score < $maxPossible * 0.7;
            })->count(),
            '不及格(60%以下)' => $totalScores->filter(function ($score) use ($exam) {
                $maxPossible = $exam->examSubjects->sum('full_score');
                return $score < $maxPossible * 0.6;
            })->count(),
        ];

        // 各科目分布
        $subjectDistributions = $scores->groupBy('subject_id')->map(function ($subjectScores) {
            $subject = $subjectScores->first()->subject;
            $validScores = $subjectScores->pluck('score');
            
            return [
                'subject_name' => $subject->name,
                'distribution' => [
                    '90-100' => $validScores->filter(fn($s) => $s >= 90)->count(),
                    '80-89' => $validScores->filter(fn($s) => $s >= 80 && $s < 90)->count(),
                    '70-79' => $validScores->filter(fn($s) => $s >= 70 && $s < 80)->count(),
                    '60-69' => $validScores->filter(fn($s) => $s >= 60 && $s < 70)->count(),
                    '0-59' => $validScores->filter(fn($s) => $s < 60)->count(),
                ],
                'statistics' => [
                    'mean' => round($validScores->avg(), 2),
                    'median' => $this->calculateMedian($validScores->toArray()),
                    'mode' => $this->calculateMode($validScores->toArray()),
                    'std_dev' => round($this->calculateStandardDeviation($validScores->toArray()), 2),
                ]
            ];
        })->values();

        return response()->json([
            'exam_name' => $exam->name,
            'total_distribution' => $totalDistribution,
            'subject_distributions' => $subjectDistributions,
            'summary' => [
                'total_students' => $totalScores->count(),
                'average_total_score' => round($totalScores->avg(), 2),
                'highest_total_score' => $totalScores->max(),
                'lowest_total_score' => $totalScores->min(),
            ]
        ]);
    }

    /**
     * Get class progress analysis.
     */
    public function getClassProgress(Classes $class): JsonResponse
    {
        // 获取班级最近5次考试的成绩
        $recentExams = Exam::whereHas('scores.student', function ($query) use ($class) {
                $query->where('class_id', $class->id);
            })
            ->orderBy('start_date', 'desc')
            ->limit(5)
            ->get();

        $progress = $recentExams->map(function ($exam) use ($class) {
            $scores = Score::where('exam_id', $exam->id)
                ->whereHas('student', function ($query) use ($class) {
                    $query->where('class_id', $class->id);
                })
                ->where('is_absent', false)
                ->with(['subject'])
                ->get();

            if ($scores->isEmpty()) {
                return null;
            }

            $totalScores = $scores->groupBy('student_id')->map(function ($studentScores) {
                return $studentScores->sum('score');
            });

            $subjectAverages = $scores->groupBy('subject_id')->map(function ($subjectScores) {
                $subject = $subjectScores->first()->subject;
                return [
                    'subject_name' => $subject->name,
                    'average' => round($subjectScores->avg('score'), 2),
                ];
            })->values();

            return [
                'exam_name' => $exam->name,
                'exam_date' => $exam->start_date,
                'class_average' => round($totalScores->avg(), 2),
                'participant_count' => $totalScores->count(),
                'subject_averages' => $subjectAverages,
            ];
        })->filter()->values();

        return response()->json([
            'class_name' => $class->name,
            'grade_name' => $class->grade->name,
            'progress_data' => $progress,
            'trend_analysis' => $this->analyzeClassTrend($progress),
        ]);
    }

    /**
     * Get student progress analysis.
     */
    public function getStudentProgress(Student $student): JsonResponse
    {
        // 获取学生最近10次考试的成绩
        $recentScores = Score::where('student_id', $student->id)
            ->where('is_absent', false)
            ->with(['exam', 'subject'])
            ->orderBy('created_at', 'desc')
            ->limit(50) // 假设每次考试5-6科，取最近10次考试
            ->get();

        $examProgress = $recentScores->groupBy('exam_id')->map(function ($examScores) {
            $exam = $examScores->first()->exam;
            $totalScore = $examScores->sum('score');
            
            return [
                'exam_name' => $exam->name,
                'exam_date' => $exam->start_date,
                'total_score' => $totalScore,
                'subject_count' => $examScores->count(),
                'average_score' => round($totalScore / $examScores->count(), 2),
                'subject_scores' => $examScores->map(function ($score) {
                    return [
                        'subject_name' => $score->subject->name,
                        'score' => $score->score,
                        'class_rank' => $score->class_rank,
                        'grade_rank' => $score->grade_rank,
                    ];
                })->values(),
            ];
        })->sortBy('exam_date')->values();

        // 科目进步分析
        $subjectProgress = $recentScores->groupBy('subject_id')->map(function ($subjectScores) {
            $subject = $subjectScores->first()->subject;
            $scores = $subjectScores->sortBy('created_at');
            
            return [
                'subject_name' => $subject->name,
                'exam_count' => $scores->count(),
                'average_score' => round($scores->avg('score'), 2),
                'best_score' => $scores->max('score'),
                'recent_trend' => $this->calculateSubjectTrend($scores),
                'score_history' => $scores->map(function ($score) {
                    return [
                        'exam_name' => $score->exam->name,
                        'score' => $score->score,
                        'class_rank' => $score->class_rank,
                        'grade_rank' => $score->grade_rank,
                    ];
                })->values(),
            ];
        })->values();

        return response()->json([
            'student_name' => $student->user->real_name,
            'student_id' => $student->student_id,
            'class_name' => $student->class->name,
            'exam_progress' => $examProgress,
            'subject_progress' => $subjectProgress,
            'overall_trend' => $this->analyzeStudentTrend($examProgress),
        ]);
    }

    /**
     * Get subject performance analysis.
     */
    public function getSubjectPerformance(Subject $subject): JsonResponse
    {
        // 获取该科目最近10次考试的数据
        $recentExamSubjects = $subject->examSubjects()
            ->with(['exam'])
            ->orderBy('exam_date', 'desc')
            ->limit(10)
            ->get();

        $performance = $recentExamSubjects->map(function ($examSubject) use ($subject) {
            $scores = Score::where('exam_id', $examSubject->exam_id)
                ->where('subject_id', $subject->id)
                ->where('is_absent', false)
                ->with(['student.class.grade'])
                ->get();

            if ($scores->isEmpty()) {
                return null;
            }

            $validScores = $scores->pluck('score');
            
            return [
                'exam_name' => $examSubject->exam->name,
                'exam_date' => $examSubject->exam_date,
                'participant_count' => $scores->count(),
                'average_score' => round($validScores->avg(), 2),
                'pass_rate' => round($validScores->filter(fn($s) => $s >= $subject->pass_score)->count() / $validScores->count() * 100, 2),
                'excellent_rate' => round($validScores->filter(fn($s) => $s >= 90)->count() / $validScores->count() * 100, 2),
                'difficulty_coefficient' => round($validScores->avg() / $examSubject->full_score, 3),
                'grade_performance' => $scores->groupBy('student.class.grade_id')->map(function ($gradeScores) {
                    $grade = $gradeScores->first()->student->class->grade;
                    return [
                        'grade_name' => $grade->name,
                        'average' => round($gradeScores->avg('score'), 2),
                        'count' => $gradeScores->count(),
                    ];
                })->values(),
            ];
        })->filter()->values();

        return response()->json([
            'subject_name' => $subject->name,
            'performance_data' => $performance,
            'trend_analysis' => $this->analyzeSubjectPerformanceTrend($performance),
            'recommendations' => $this->generateSubjectRecommendations($performance),
        ]);
    }

    // 辅助方法
    private function calculateMedian(array $numbers): float
    {
        sort($numbers);
        $count = count($numbers);
        if ($count === 0) return 0;
        if ($count % 2 === 0) {
            return ($numbers[$count / 2 - 1] + $numbers[$count / 2]) / 2;
        } else {
            return $numbers[intval($count / 2)];
        }
    }

    private function calculateMode(array $numbers): float
    {
        if (empty($numbers)) return 0;
        $frequency = array_count_values($numbers);
        $maxFreq = max($frequency);
        $modes = array_keys($frequency, $maxFreq);
        return $modes[0];
    }

    private function calculateStandardDeviation(array $numbers): float
    {
        if (count($numbers) === 0) return 0;
        $mean = array_sum($numbers) / count($numbers);
        $squaredDiffs = array_map(fn($x) => pow($x - $mean, 2), $numbers);
        return sqrt(array_sum($squaredDiffs) / count($numbers));
    }

    private function analyzeTrendDirection($trends): string
    {
        if ($trends->count() < 2) return '数据不足';
        $recent = $trends->take(3)->avg('total_average');
        $earlier = $trends->skip(3)->avg('total_average');
        if ($recent > $earlier + 5) return '成绩上升';
        if ($recent < $earlier - 5) return '成绩下降';
        return '成绩稳定';
    }

    private function analyzeClassTrend($progress): string
    {
        if ($progress->count() < 2) return '数据不足';
        $recent = $progress->take(2)->avg('class_average');
        $earlier = $progress->skip(2)->avg('class_average');
        if ($recent > $earlier + 10) return '班级进步明显';
        if ($recent < $earlier - 10) return '班级成绩下滑';
        return '班级成绩稳定';
    }

    private function analyzeStudentTrend($examProgress): string
    {
        if ($examProgress->count() < 2) return '数据不足';
        $recent = $examProgress->take(2)->avg('total_score');
        $earlier = $examProgress->skip(2)->avg('total_score');
        if ($recent > $earlier + 20) return '进步明显';
        if ($recent < $earlier - 20) return '成绩下滑';
        return '成绩稳定';
    }

    private function calculateSubjectTrend($scores): string
    {
        if ($scores->count() < 2) return '数据不足';
        $recent = $scores->take(2)->avg('score');
        $earlier = $scores->skip(2)->avg('score');
        if ($recent > $earlier + 5) return '进步';
        if ($recent < $earlier - 5) return '退步';
        return '稳定';
    }

    private function analyzeSubjectPerformanceTrend($performance): string
    {
        if ($performance->count() < 2) return '数据不足';
        $recent = $performance->take(3)->avg('average_score');
        $earlier = $performance->skip(3)->avg('average_score');
        if ($recent > $earlier + 5) return '整体水平提升';
        if ($recent < $earlier - 5) return '整体水平下降';
        return '整体水平稳定';
    }

    private function generateSubjectRecommendations($performance): array
    {
        $recommendations = [];
        
        if ($performance->count() === 0) {
            return ['暂无足够数据生成建议'];
        }

        $avgPassRate = $performance->avg('pass_rate');
        $avgDifficulty = $performance->avg('difficulty_coefficient');

        if ($avgPassRate < 60) {
            $recommendations[] = '及格率偏低，建议调整教学方法，加强基础知识讲解';
        }

        if ($avgDifficulty > 0.8) {
            $recommendations[] = '试题难度偏低，建议适当增加题目难度以提高区分度';
        } elseif ($avgDifficulty < 0.4) {
            $recommendations[] = '试题难度偏高，建议适当降低难度或加强教学';
        }

        if (empty($recommendations)) {
            $recommendations[] = '该科目教学效果良好，继续保持';
        }

        return $recommendations;
    }
}
