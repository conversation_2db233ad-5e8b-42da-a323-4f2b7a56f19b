<?php

declare(strict_types=1);

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the tenant dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $tenant = tenant();
        
        // 获取用户角色和权限
        $userRole = $user->getRole();
        $userPermissions = $user->permissions;
        
        // 获取租户的订阅模块
        $subscribedModules = $this->getSubscribedModules($tenant);
        
        // 根据用户角色获取相关数据
        $dashboardData = $this->getDashboardDataByRole($user, $userRole);
        
        return Inertia::render('Tenant/Dashboard', [
            'user' => [
                'id' => $user->id,
                'name' => $user->display_name,
                'email' => $user->email,
                'role' => $userRole,
                'permissions' => $userPermissions,
                'is_owner' => $user->isOwner(),
            ],
            'tenant' => [
                'id' => $tenant->id,
                'name' => $tenant->name ?? '学校',
                'domain' => $tenant->domains->first()?->domain,
                'subscribed_modules' => $subscribedModules,
            ],
            'modules' => [
                'core' => [
                    'name' => '核心模块',
                    'description' => '用户管理、权限控制、基础设置',
                    'available' => true,
                    'icon' => 'cog-6-tooth'
                ],
                'score' => [
                    'name' => '成绩分析',
                    'description' => '考试管理、成绩录入、数据分析',
                    'available' => in_array('score', $subscribedModules),
                    'icon' => 'chart-bar'
                ],
                'salary' => [
                    'name' => '工资管理',
                    'description' => '薪资计算、工资统计、报表生成',
                    'available' => in_array('salary', $subscribedModules),
                    'icon' => 'currency-dollar'
                ]
            ],
            'stats' => $dashboardData['stats'] ?? [],
            'recent_activities' => $dashboardData['activities'] ?? [],
            'quick_actions' => $this->getQuickActionsByRole($userRole, $subscribedModules),
        ]);
    }

    /**
     * Get subscribed modules for the tenant.
     */
    private function getSubscribedModules($tenant): array
    {
        // 基础模块总是可用
        $modules = ['core'];
        
        // 检查订阅状态
        if ($tenant->subscribed('default')) {
            $subscription = $tenant->subscription('default');
            
            // 这里需要根据实际的订阅计划来确定可用模块
            // 暂时使用简单的逻辑
            if ($subscription->stripe_price === env('STRIPE_PRICE_SCORE_MONTHLY')) {
                $modules[] = 'score';
            } elseif ($subscription->stripe_price === env('STRIPE_PRICE_COMPLETE_MONTHLY')) {
                $modules[] = 'score';
                $modules[] = 'salary';
            }
        }
        
        return $modules;
    }

    /**
     * Get dashboard data based on user role.
     */
    private function getDashboardDataByRole($user, string $role): array
    {
        switch ($role) {
            case 'principal':
            case 'academic_director':
                return $this->getAdminDashboardData();
                
            case 'grade_director':
                return $this->getGradeDirectorDashboardData($user);
                
            case 'class_teacher':
                return $this->getClassTeacherDashboardData($user);
                
            case 'subject_teacher':
                return $this->getSubjectTeacherDashboardData($user);
                
            case 'student':
                return $this->getStudentDashboardData($user);
                
            case 'parent':
                return $this->getParentDashboardData($user);
                
            default:
                return ['stats' => [], 'activities' => []];
        }
    }

    /**
     * Get admin dashboard data.
     */
    private function getAdminDashboardData(): array
    {
        return [
            'stats' => [
                [
                    'name' => '学生总数',
                    'value' => '1,234',
                    'change' => '+12%',
                    'trend' => 'up'
                ],
                [
                    'name' => '教师总数',
                    'value' => '89',
                    'change' => '+3%',
                    'trend' => 'up'
                ],
                [
                    'name' => '班级总数',
                    'value' => '36',
                    'change' => '0%',
                    'trend' => 'stable'
                ],
                [
                    'name' => '本月考试',
                    'value' => '8',
                    'change' => '+2',
                    'trend' => 'up'
                ]
            ],
            'activities' => [
                [
                    'type' => 'exam',
                    'title' => '期中考试成绩录入完成',
                    'description' => '高一年级期中考试成绩已全部录入',
                    'time' => '2小时前'
                ],
                [
                    'type' => 'user',
                    'title' => '新教师注册',
                    'description' => '张老师已加入数学组',
                    'time' => '1天前'
                ]
            ]
        ];
    }

    /**
     * Get grade director dashboard data.
     */
    private function getGradeDirectorDashboardData($user): array
    {
        return [
            'stats' => [
                [
                    'name' => '年级学生数',
                    'value' => '320',
                    'change' => '+5%',
                    'trend' => 'up'
                ],
                [
                    'name' => '年级班级数',
                    'value' => '8',
                    'change' => '0%',
                    'trend' => 'stable'
                ]
            ],
            'activities' => []
        ];
    }

    /**
     * Get class teacher dashboard data.
     */
    private function getClassTeacherDashboardData($user): array
    {
        return [
            'stats' => [
                [
                    'name' => '班级学生数',
                    'value' => '42',
                    'change' => '0%',
                    'trend' => 'stable'
                ]
            ],
            'activities' => []
        ];
    }

    /**
     * Get subject teacher dashboard data.
     */
    private function getSubjectTeacherDashboardData($user): array
    {
        return [
            'stats' => [
                [
                    'name' => '教授班级数',
                    'value' => '6',
                    'change' => '0%',
                    'trend' => 'stable'
                ]
            ],
            'activities' => []
        ];
    }

    /**
     * Get student dashboard data.
     */
    private function getStudentDashboardData($user): array
    {
        return [
            'stats' => [
                [
                    'name' => '本学期考试',
                    'value' => '12',
                    'change' => '+2',
                    'trend' => 'up'
                ]
            ],
            'activities' => []
        ];
    }

    /**
     * Get parent dashboard data.
     */
    private function getParentDashboardData($user): array
    {
        return [
            'stats' => [
                [
                    'name' => '子女人数',
                    'value' => '2',
                    'change' => '0%',
                    'trend' => 'stable'
                ]
            ],
            'activities' => []
        ];
    }

    /**
     * Get quick actions based on user role.
     */
    private function getQuickActionsByRole(string $role, array $modules): array
    {
        $actions = [];

        // 基础操作
        $actions[] = [
            'name' => '个人设置',
            'description' => '修改个人信息和密码',
            'icon' => 'user',
            'route' => 'tenant.profile'
        ];

        // 根据角色添加操作
        if (in_array($role, ['principal', 'academic_director'])) {
            $actions[] = [
                'name' => '用户管理',
                'description' => '管理教师和学生账户',
                'icon' => 'users',
                'route' => 'tenant.users.index'
            ];
        }

        // 根据模块添加操作
        if (in_array('score', $modules)) {
            if (in_array($role, ['principal', 'academic_director', 'grade_director', 'class_teacher', 'subject_teacher'])) {
                $actions[] = [
                    'name' => '成绩管理',
                    'description' => '录入和查看考试成绩',
                    'icon' => 'chart-bar',
                    'route' => 'tenant.score.index'
                ];
            }
        }

        return $actions;
    }
}
