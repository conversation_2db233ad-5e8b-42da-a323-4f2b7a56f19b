<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Exception;

class SocialiteController extends Controller
{
    /**
     * Redirect the user to the provider authentication page.
     */
    public function redirect(string $provider): RedirectResponse
    {
        // 支持的社交登录提供商列表
        $supportedProviders = [
            'github', 'google', 'weixin', 'qq',
            'alipay', 'weibo'
        ];

        // 验证提供商
        if (!in_array($provider, $supportedProviders)) {
            return redirect()->route('tenant.login')
                ->with('error', '不支持的社交登录提供商: ' . $provider);
        }

        try {
            return Socialite::driver($provider)->redirect();
        } catch (Exception $e) {
            return redirect()->route('tenant.login')
                ->with('error', '社交登录重定向失败: ' . $e->getMessage());
        }
    }

    /**
     * Handle the provider callback.
     */
    public function callback(string $provider): RedirectResponse
    {
        try {
            // Get user data from provider
            $socialUser = Socialite::driver($provider)->user();

            // 验证获取到的用户信息
            if (!$socialUser->getId()) {
                throw new Exception('未能从' . $provider . '获取用户ID');
            }

            // Find existing user by provider id
            $user = User::where('provider_id', $socialUser->getId())
                ->where('provider', $provider)
                ->first();

            // If user doesn't exist, check if email exists
            if (!$user && $socialUser->getEmail()) {
                $user = User::where('email', $socialUser->getEmail())->first();

                // If user exists, update provider details
                if ($user) {
                    $user->update([
                        'provider' => $provider,
                        'provider_id' => $socialUser->getId(),
                        'last_login_at' => now(),
                    ]);
                }
            }

            // If user still doesn't exist, create new user
            if (!$user) {
                // 生成用户名
                $username = $this->generateUniqueUsername($socialUser, $provider);

                // 生成邮箱（如果提供商没有返回邮箱）
                $email = $socialUser->getEmail() ?? $socialUser->getId() . '@' . $provider . '.social';

                $user = User::create([
                    'name' => $socialUser->getName() ?? $socialUser->getNickname() ?? '用户',
                    'username' => $username,
                    'email' => $email,
                    'password' => Hash::make(Str::random(32)), // 随机密码
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                    'avatar_url' => $socialUser->getAvatar(),
                    'email_verified_at' => now(), // 社交登录自动验证邮箱
                    'is_active' => true,
                    'last_login_at' => now(),
                ]);
            } else {
                // 更新最后登录时间
                $user->update(['last_login_at' => now()]);
            }

            // Login user
            Auth::login($user);

            return redirect()->intended(route('tenant.dashboard'))
                ->with('success', '通过 ' . ucfirst($provider) . ' 登录成功！');

        } catch (Exception $e) {
            Log::error('Social login failed', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('tenant.login')
                ->with('error', '社交登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成唯一的用户名
     */
    private function generateUniqueUsername($socialUser, string $provider): string
    {
        // 尝试使用昵称或真实姓名
        $baseUsername = $socialUser->getNickname() ??
                       Str::slug($socialUser->getName() ?? 'user');

        // 如果为空，使用提供商名称
        if (empty($baseUsername)) {
            $baseUsername = $provider . '_user';
        }

        $username = $baseUsername;
        $counter = 1;

        // 确保用户名唯一
        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . '_' . $counter;
            $counter++;
        }

        return $username;
    }
}
