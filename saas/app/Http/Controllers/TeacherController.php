<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Teacher;
use App\Models\Department;
use App\Models\User;
use App\Models\Role;
use App\Models\Classes;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class TeacherController extends Controller
{
    /**
     * Display a listing of teachers.
     */
    public function index(Request $request): Response
    {
        $query = Teacher::with(['user', 'department'])
            ->orderBy('created_at', 'desc');

        // 应用筛选条件
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('teacher_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('real_name', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('department_id')) {
            $query->where('department_id', $request->get('department_id'));
        }

        if ($request->filled('position')) {
            $query->where('position', 'like', '%' . $request->get('position') . '%');
        }

        $teachers = $query->paginate(20)->withQueryString();

        return Inertia::render('tenant/teachers/Index', [
            'teachers' => $teachers,
            'departments' => Department::all(),
            'filters' => $request->only(['search', 'department_id', 'position'])
        ]);
    }

    /**
     * Show the form for creating a new teacher.
     */
    public function create(): Response
    {
        return Inertia::render('tenant/teachers/Create', [
            'departments' => Department::all(),
            'roles' => Role::whereIn('name', ['校长', '教务主任', '德育主任', '年级主任', '任课教师'])->get(),
        ]);
    }

    /**
     * Store a newly created teacher.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'real_name' => 'required|string|max:100',
            'teacher_id' => 'required|string|max:50|unique:teachers,teacher_id',
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'hire_date' => 'nullable|date',
            'email' => 'nullable|email|unique:users,email',
            'role_name' => 'required|string',
        ]);

        try {
            DB::beginTransaction();

            // 创建用户账号
            $user = User::create([
                'name' => $validated['real_name'],
                'real_name' => $validated['real_name'],
                'username' => $validated['teacher_id'],
                'email' => $validated['email'] ?? $validated['teacher_id'] . '@teacher.school.edu',
                'password' => Hash::make('password'), // 默认密码
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            // 分配角色
            $role = Role::where('name', $validated['role_name'])->first();
            if ($role) {
                $user->roles()->attach($role->id);
            }

            // 创建教师记录
            $teacher = Teacher::create([
                'user_id' => $user->id,
                'teacher_id' => $validated['teacher_id'],
                'department_id' => $validated['department_id'],
                'position' => $validated['position'],
                'hire_date' => $validated['hire_date'],
            ]);

            DB::commit();

            return response()->json([
                'message' => '教师创建成功',
                'teacher' => $teacher->load(['user', 'department'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified teacher.
     */
    public function show(Teacher $teacher): Response
    {
        $teacher->load(['user', 'department', 'classes', 'grades']);

        return Inertia::render('tenant/teachers/Show', [
            'teacher' => $teacher,
        ]);
    }

    /**
     * Update the specified teacher.
     */
    public function update(Request $request, Teacher $teacher): JsonResponse
    {
        $validated = $request->validate([
            'real_name' => 'required|string|max:100',
            'teacher_id' => 'required|string|max:50|unique:teachers,teacher_id,' . $teacher->id,
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'hire_date' => 'nullable|date',
        ]);

        try {
            DB::beginTransaction();

            // 更新用户信息
            $teacher->user->update([
                'name' => $validated['real_name'],
                'real_name' => $validated['real_name'],
                'username' => $validated['teacher_id'],
            ]);

            // 更新教师信息
            $teacher->update($validated);

            DB::commit();

            return response()->json([
                'message' => '教师信息更新成功',
                'teacher' => $teacher->load(['user', 'department'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified teacher.
     */
    public function destroy(Teacher $teacher): JsonResponse
    {
        try {
            DB::beginTransaction();

            // 检查是否有关联的班级或年级
            if ($teacher->classes()->count() > 0 || $teacher->grades()->count() > 0) {
                return response()->json([
                    'message' => '该教师还有关联的班级或年级，无法删除'
                ], 422);
            }

            // 删除教师记录（会级联删除用户）
            $teacher->delete();

            DB::commit();

            return response()->json([
                'message' => '教师删除成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teacher's classes.
     */
    public function getClasses(Teacher $teacher): JsonResponse
    {
        $classes = $teacher->classes()
            ->with(['grade', 'students'])
            ->get()
            ->map(function ($class) {
                return [
                    'id' => $class->id,
                    'name' => $class->name,
                    'grade' => $class->grade->name,
                    'student_count' => $class->students->count(),
                ];
            });

        return response()->json($classes);
    }

    /**
     * Get teacher's subjects.
     */
    public function getSubjects(Teacher $teacher): JsonResponse
    {
        // 这里需要根据实际业务逻辑来获取教师教授的科目
        // 可能需要创建teacher_subjects关联表
        $subjects = Subject::all(); // 临时返回所有科目

        return response()->json($subjects);
    }

    /**
     * Get teacher performance data.
     */
    public function getPerformance(Teacher $teacher): JsonResponse
    {
        $classes = $teacher->classes()->with(['students.scores'])->get();
        
        $performance = [
            'total_classes' => $classes->count(),
            'total_students' => $classes->sum(function ($class) {
                return $class->students->count();
            }),
            'class_performance' => $classes->map(function ($class) {
                $scores = $class->students->flatMap->scores->where('is_absent', false);
                return [
                    'class_name' => $class->name,
                    'student_count' => $class->students->count(),
                    'average_score' => $scores->avg('score'),
                    'pass_rate' => $scores->count() > 0 ? 
                        $scores->filter(function ($score) {
                            return $score->score >= ($score->subject->pass_score ?? 60);
                        })->count() / $scores->count() * 100 : 0
                ];
            })
        ];

        return response()->json($performance);
    }
}
