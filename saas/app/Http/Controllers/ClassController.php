<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Classes;
use App\Models\Grade;
use App\Models\Teacher;
use App\Models\Student;
use App\Models\Score;
use App\Models\Exam;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class ClassController extends Controller
{
    /**
     * Display a listing of classes.
     */
    public function index(Request $request): Response
    {
        $query = Classes::with(['grade', 'classTeacher.user', 'students'])
            ->orderBy('grade_id')
            ->orderBy('name');

        // 应用筛选条件
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        if ($request->filled('grade_id')) {
            $query->where('grade_id', $request->get('grade_id'));
        }

        $classes = $query->paginate(20)->withQueryString();

        // 为每个班级添加学生数量
        $classes->getCollection()->transform(function ($class) {
            $class->student_count = $class->students->count();
            return $class;
        });

        return Inertia::render('tenant/classes/Index', [
            'classes' => $classes,
            'grades' => Grade::all(),
            'filters' => $request->only(['search', 'grade_id'])
        ]);
    }

    /**
     * Show the form for creating a new class.
     */
    public function create(): Response
    {
        return Inertia::render('tenant/classes/Create', [
            'grades' => Grade::all(),
            'teachers' => Teacher::with('user')->get(),
        ]);
    }

    /**
     * Store a newly created class.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'grade_id' => 'required|exists:grades,id',
            'class_teacher_id' => 'nullable|exists:teachers,id',
            'capacity' => 'nullable|integer|min:1|max:100',
        ]);

        // 检查班级名称在同一年级内是否唯一
        $exists = Classes::where('grade_id', $validated['grade_id'])
            ->where('name', $validated['name'])
            ->exists();

        if ($exists) {
            return response()->json([
                'message' => '该年级已存在同名班级'
            ], 422);
        }

        try {
            $class = Classes::create($validated);

            return response()->json([
                'message' => '班级创建成功',
                'class' => $class->load(['grade', 'classTeacher.user'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified class.
     */
    public function show(Classes $class): Response
    {
        $class->load(['grade', 'classTeacher.user', 'students.user']);

        return Inertia::render('tenant/classes/Show', [
            'class' => $class,
        ]);
    }

    /**
     * Update the specified class.
     */
    public function update(Request $request, Classes $class): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'grade_id' => 'required|exists:grades,id',
            'class_teacher_id' => 'nullable|exists:teachers,id',
            'capacity' => 'nullable|integer|min:1|max:100',
        ]);

        // 检查班级名称在同一年级内是否唯一（排除当前班级）
        $exists = Classes::where('grade_id', $validated['grade_id'])
            ->where('name', $validated['name'])
            ->where('id', '!=', $class->id)
            ->exists();

        if ($exists) {
            return response()->json([
                'message' => '该年级已存在同名班级'
            ], 422);
        }

        try {
            $class->update($validated);

            return response()->json([
                'message' => '班级信息更新成功',
                'class' => $class->load(['grade', 'classTeacher.user'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified class.
     */
    public function destroy(Classes $class): JsonResponse
    {
        try {
            // 检查是否有学生
            if ($class->students()->count() > 0) {
                return response()->json([
                    'message' => '该班级还有学生，无法删除'
                ], 422);
            }

            $class->delete();

            return response()->json([
                'message' => '班级删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get class students.
     */
    public function getStudents(Classes $class): JsonResponse
    {
        $students = $class->students()
            ->with('user')
            ->orderBy('student_id')
            ->get()
            ->map(function ($student) {
                return [
                    'id' => $student->id,
                    'student_id' => $student->student_id,
                    'name' => $student->user->real_name,
                    'gender' => $student->gender,
                    'birth_date' => $student->birth_date,
                    'admission_date' => $student->admission_date,
                    'parent_name' => $student->parent_name,
                    'parent_phone' => $student->parent_phone,
                ];
            });

        return response()->json($students);
    }

    /**
     * Get class exam scores.
     */
    public function getExamScores(Classes $class, Exam $exam): JsonResponse
    {
        $scores = Score::whereHas('student', function ($query) use ($class) {
                $query->where('class_id', $class->id);
            })
            ->where('exam_id', $exam->id)
            ->with(['student.user', 'subject'])
            ->get()
            ->groupBy('student_id')
            ->map(function ($studentScores) {
                $student = $studentScores->first()->student;
                return [
                    'student' => [
                        'id' => $student->id,
                        'name' => $student->user->real_name,
                        'student_id' => $student->student_id,
                    ],
                    'scores' => $studentScores->map(function ($score) {
                        return [
                            'subject' => $score->subject->name,
                            'score' => $score->score,
                            'is_absent' => $score->is_absent,
                            'class_rank' => $score->class_rank,
                            'grade_rank' => $score->grade_rank,
                        ];
                    })
                ];
            })
            ->values();

        return response()->json($scores);
    }

    /**
     * Get class exam analysis.
     */
    public function getExamAnalysis(Classes $class, Exam $exam): JsonResponse
    {
        $scores = Score::whereHas('student', function ($query) use ($class) {
                $query->where('class_id', $class->id);
            })
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with('subject')
            ->get();

        $analysis = [
            'total_students' => $class->students()->count(),
            'participated_students' => $scores->groupBy('student_id')->count(),
            'subject_analysis' => $scores->groupBy('subject_id')->map(function ($subjectScores) {
                $subject = $subjectScores->first()->subject;
                $validScores = $subjectScores->pluck('score');
                
                return [
                    'subject_name' => $subject->name,
                    'average_score' => $validScores->avg(),
                    'highest_score' => $validScores->max(),
                    'lowest_score' => $validScores->min(),
                    'pass_rate' => $validScores->filter(function ($score) use ($subject) {
                        return $score >= ($subject->pass_score ?? 60);
                    })->count() / $validScores->count() * 100,
                    'score_distribution' => [
                        'excellent' => $validScores->filter(fn($s) => $s >= 90)->count(),
                        'good' => $validScores->filter(fn($s) => $s >= 80 && $s < 90)->count(),
                        'average' => $validScores->filter(fn($s) => $s >= 70 && $s < 80)->count(),
                        'below_average' => $validScores->filter(fn($s) => $s < 70)->count(),
                    ]
                ];
            })->values()
        ];

        return response()->json($analysis);
    }

    /**
     * Get class exam rankings.
     */
    public function getExamRankings(Classes $class, Exam $exam): JsonResponse
    {
        // 计算总分排名
        $studentTotalScores = Score::whereHas('student', function ($query) use ($class) {
                $query->where('class_id', $class->id);
            })
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['student.user', 'subject'])
            ->get()
            ->groupBy('student_id')
            ->map(function ($studentScores) {
                $student = $studentScores->first()->student;
                $totalScore = $studentScores->sum('score');
                
                return [
                    'student_id' => $student->id,
                    'student_name' => $student->user->real_name,
                    'student_number' => $student->student_id,
                    'total_score' => $totalScore,
                    'subject_scores' => $studentScores->map(function ($score) {
                        return [
                            'subject' => $score->subject->name,
                            'score' => $score->score,
                            'class_rank' => $score->class_rank,
                        ];
                    })
                ];
            })
            ->sortByDesc('total_score')
            ->values()
            ->map(function ($student, $index) {
                $student['total_rank'] = $index + 1;
                return $student;
            });

        return response()->json($studentTotalScores);
    }

    /**
     * Get class statistics.
     */
    public function getStatistics(Classes $class): JsonResponse
    {
        $students = $class->students();
        $scores = Score::whereHas('student', function ($query) use ($class) {
            $query->where('class_id', $class->id);
        })->where('is_absent', false);

        $statistics = [
            'student_count' => $students->count(),
            'male_count' => $students->where('gender', 'male')->count(),
            'female_count' => $students->where('gender', 'female')->count(),
            'total_exams' => $scores->distinct('exam_id')->count(),
            'average_score' => $scores->avg('score'),
            'recent_performance' => $scores->with(['exam', 'subject'])
                ->orderBy('created_at', 'desc')
                ->limit(100)
                ->get()
                ->groupBy('exam_id')
                ->take(5)
                ->map(function ($examScores) {
                    $exam = $examScores->first()->exam;
                    return [
                        'exam_name' => $exam->name,
                        'exam_date' => $exam->start_date,
                        'average_score' => $examScores->avg('score'),
                        'participant_count' => $examScores->groupBy('student_id')->count(),
                    ];
                })
                ->values()
        ];

        return response()->json($statistics);
    }
}
