<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Grade;
use App\Models\Classes;
use App\Models\User;
use App\Models\Role;
use App\Models\Score;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index(Request $request): Response
    {
        $query = Student::with(['user', 'class.grade'])
            ->orderBy('created_at', 'desc');

        // 应用筛选条件
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('student_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('real_name', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('grade_id')) {
            $query->whereHas('class', function ($q) use ($request) {
                $q->where('grade_id', $request->get('grade_id'));
            });
        }

        if ($request->filled('class_id')) {
            $query->where('class_id', $request->get('class_id'));
        }

        if ($request->filled('gender')) {
            $query->where('gender', $request->get('gender'));
        }

        $students = $query->paginate(20)->withQueryString();

        return Inertia::render('tenant/students/Index', [
            'students' => $students,
            'grades' => Grade::with('classes')->get(),
            'classes' => Classes::with('grade')->get(),
            'filters' => $request->only(['search', 'grade_id', 'class_id', 'gender'])
        ]);
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): Response
    {
        return Inertia::render('tenant/students/Create', [
            'grades' => Grade::with('classes')->get(),
            'classes' => Classes::with('grade')->get(),
        ]);
    }

    /**
     * Store a newly created student.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'real_name' => 'required|string|max:100',
            'student_id' => 'required|string|max:50|unique:students,student_id',
            'class_id' => 'required|exists:classes,id',
            'gender' => 'required|in:male,female',
            'birth_date' => 'nullable|date',
            'admission_date' => 'nullable|date',
            'address' => 'nullable|string',
            'parent_name' => 'nullable|string|max:100',
            'parent_phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|unique:users,email',
        ]);

        try {
            DB::beginTransaction();

            // 创建用户账号
            $user = User::create([
                'name' => $validated['real_name'],
                'real_name' => $validated['real_name'],
                'username' => $validated['student_id'],
                'email' => $validated['email'] ?? $validated['student_id'] . '@student.school.edu',
                'password' => Hash::make('password'), // 默认密码
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            // 分配学生角色
            $studentRole = Role::where('name', '学生')->first();
            if ($studentRole) {
                $user->roles()->attach($studentRole->id);
            }

            // 创建学生记录
            $student = Student::create([
                'user_id' => $user->id,
                'student_id' => $validated['student_id'],
                'class_id' => $validated['class_id'],
                'gender' => $validated['gender'],
                'birth_date' => $validated['birth_date'],
                'admission_date' => $validated['admission_date'],
                'address' => $validated['address'],
                'parent_name' => $validated['parent_name'],
                'parent_phone' => $validated['parent_phone'],
            ]);

            DB::commit();

            return response()->json([
                'message' => '学生创建成功',
                'student' => $student->load(['user', 'class.grade'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified student.
     */
    public function show(Student $student): Response
    {
        $student->load(['user', 'class.grade', 'scores.exam', 'scores.subject']);

        return Inertia::render('tenant/students/Show', [
            'student' => $student,
        ]);
    }

    /**
     * Update the specified student.
     */
    public function update(Request $request, Student $student): JsonResponse
    {
        $validated = $request->validate([
            'real_name' => 'required|string|max:100',
            'student_id' => 'required|string|max:50|unique:students,student_id,' . $student->id,
            'class_id' => 'required|exists:classes,id',
            'gender' => 'required|in:male,female',
            'birth_date' => 'nullable|date',
            'admission_date' => 'nullable|date',
            'address' => 'nullable|string',
            'parent_name' => 'nullable|string|max:100',
            'parent_phone' => 'nullable|string|max:20',
        ]);

        try {
            DB::beginTransaction();

            // 更新用户信息
            $student->user->update([
                'name' => $validated['real_name'],
                'real_name' => $validated['real_name'],
                'username' => $validated['student_id'],
            ]);

            // 更新学生信息
            $student->update($validated);

            DB::commit();

            return response()->json([
                'message' => '学生信息更新成功',
                'student' => $student->load(['user', 'class.grade'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified student.
     */
    public function destroy(Student $student): JsonResponse
    {
        try {
            DB::beginTransaction();

            // 删除学生记录（会级联删除用户）
            $student->delete();

            DB::commit();

            return response()->json([
                'message' => '学生删除成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student scores.
     */
    public function getScores(Student $student): JsonResponse
    {
        $scores = $student->scores()
            ->with(['exam', 'subject'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($scores);
    }

    /**
     * Get student analysis data.
     */
    public function getAnalysis(Student $student): JsonResponse
    {
        $scores = $student->scores()
            ->with(['exam', 'subject'])
            ->where('is_absent', false)
            ->get();

        $analysis = [
            'total_exams' => $scores->groupBy('exam_id')->count(),
            'average_score' => $scores->avg('score'),
            'best_subject' => $scores->groupBy('subject_id')
                ->map(function ($subjectScores) {
                    return [
                        'subject' => $subjectScores->first()->subject,
                        'average' => $subjectScores->avg('score')
                    ];
                })
                ->sortByDesc('average')
                ->first(),
            'score_trend' => $scores->groupBy('exam.start_date')
                ->map(function ($examScores) {
                    return [
                        'date' => $examScores->first()->exam->start_date,
                        'average' => $examScores->avg('score')
                    ];
                })
                ->sortBy('date')
                ->values()
        ];

        return response()->json($analysis);
    }

    /**
     * Get student rankings.
     */
    public function getRankings(Student $student): JsonResponse
    {
        $rankings = $student->scores()
            ->with(['exam', 'subject'])
            ->where('is_absent', false)
            ->whereNotNull('class_rank')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($score) {
                return [
                    'exam' => $score->exam->name,
                    'subject' => $score->subject->name,
                    'score' => $score->score,
                    'class_rank' => $score->class_rank,
                    'grade_rank' => $score->grade_rank,
                ];
            });

        return response()->json($rankings);
    }

    /**
     * Batch import students.
     */
    public function batchImport(Request $request): JsonResponse
    {
        // TODO: 实现批量导入功能
        return response()->json([
            'message' => '批量导入功能待实现'
        ]);
    }

    /**
     * Export students template.
     */
    public function exportTemplate(): JsonResponse
    {
        // TODO: 实现导出模板功能
        return response()->json([
            'message' => '导出模板功能待实现'
        ]);
    }
}
