<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Grade;
use App\Models\Classes;
use App\Models\Student;
use App\Models\Score;
use App\Models\Exam;
use App\Models\Teacher;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class GradeController extends Controller
{
    /**
     * Display a listing of grades.
     */
    public function index(Request $request): Response
    {
        $query = Grade::with(['classes', 'gradeHead.user'])
            ->withCount(['classes', 'students'])
            ->orderBy('level');

        // 应用筛选条件
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        if ($request->filled('academic_year')) {
            $query->where('academic_year', $request->get('academic_year'));
        }

        $grades = $query->paginate(20)->withQueryString();

        return Inertia::render('tenant/grades/Index', [
            'grades' => $grades,
            'filters' => $request->only(['search', 'academic_year'])
        ]);
    }

    /**
     * Show the form for creating a new grade.
     */
    public function create(): Response
    {
        return Inertia::render('tenant/grades/Create', [
            'teachers' => Teacher::with('user')->get(),
        ]);
    }

    /**
     * Store a newly created grade.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:grades,name',
            'level' => 'required|integer|min:1|max:12',
            'academic_year' => 'required|string|max:20',
            'grade_head_id' => 'nullable|exists:teachers,id',
        ]);

        try {
            $grade = Grade::create($validated);

            return response()->json([
                'message' => '年级创建成功',
                'grade' => $grade->load(['gradeHead.user'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified grade.
     */
    public function show(Grade $grade): Response
    {
        $grade->load(['classes.classTeacher.user', 'gradeHead.user']);
        $grade->loadCount(['classes', 'students']);

        return Inertia::render('tenant/grades/Show', [
            'grade' => $grade,
        ]);
    }

    /**
     * Update the specified grade.
     */
    public function update(Request $request, Grade $grade): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:grades,name,' . $grade->id,
            'level' => 'required|integer|min:1|max:12',
            'academic_year' => 'required|string|max:20',
            'grade_head_id' => 'nullable|exists:teachers,id',
        ]);

        try {
            $grade->update($validated);

            return response()->json([
                'message' => '年级信息更新成功',
                'grade' => $grade->load(['gradeHead.user'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified grade.
     */
    public function destroy(Grade $grade): JsonResponse
    {
        try {
            // 检查是否有班级
            if ($grade->classes()->count() > 0) {
                return response()->json([
                    'message' => '该年级还有班级，无法删除'
                ], 422);
            }

            $grade->delete();

            return response()->json([
                'message' => '年级删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get grade classes.
     */
    public function getClasses(Grade $grade): JsonResponse
    {
        $classes = $grade->classes()
            ->with(['classTeacher.user'])
            ->withCount('students')
            ->orderBy('name')
            ->get()
            ->map(function ($class) {
                return [
                    'id' => $class->id,
                    'name' => $class->name,
                    'capacity' => $class->capacity,
                    'student_count' => $class->students_count,
                    'class_teacher' => $class->classTeacher ? [
                        'id' => $class->classTeacher->id,
                        'name' => $class->classTeacher->user->real_name,
                    ] : null,
                ];
            });

        return response()->json($classes);
    }

    /**
     * Get grade students.
     */
    public function getStudents(Grade $grade): JsonResponse
    {
        $students = Student::whereHas('class', function ($query) use ($grade) {
                $query->where('grade_id', $grade->id);
            })
            ->with(['user', 'class'])
            ->orderBy('student_id')
            ->get()
            ->map(function ($student) {
                return [
                    'id' => $student->id,
                    'student_id' => $student->student_id,
                    'name' => $student->user->real_name,
                    'gender' => $student->gender,
                    'class_name' => $student->class->name,
                    'admission_date' => $student->admission_date,
                ];
            });

        return response()->json($students);
    }

    /**
     * Get grade exam analysis.
     */
    public function getExamAnalysis(Grade $grade, Exam $exam): JsonResponse
    {
        $scores = Score::whereHas('student.class', function ($query) use ($grade) {
                $query->where('grade_id', $grade->id);
            })
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['student.class', 'subject'])
            ->get();

        $analysis = [
            'total_students' => Student::whereHas('class', function ($query) use ($grade) {
                $query->where('grade_id', $grade->id);
            })->count(),
            'participated_students' => $scores->groupBy('student_id')->count(),
            'class_analysis' => $scores->groupBy('student.class_id')->map(function ($classScores) {
                $class = $classScores->first()->student->class;
                $studentScores = $classScores->groupBy('student_id');
                
                return [
                    'class_name' => $class->name,
                    'student_count' => $studentScores->count(),
                    'average_score' => $classScores->avg('score'),
                    'subject_averages' => $classScores->groupBy('subject_id')->map(function ($subjectScores) {
                        $subject = $subjectScores->first()->subject;
                        return [
                            'subject_name' => $subject->name,
                            'average' => $subjectScores->avg('score'),
                        ];
                    })->values()
                ];
            })->values(),
            'subject_analysis' => $scores->groupBy('subject_id')->map(function ($subjectScores) {
                $subject = $subjectScores->first()->subject;
                $validScores = $subjectScores->pluck('score');
                
                return [
                    'subject_name' => $subject->name,
                    'average_score' => $validScores->avg(),
                    'highest_score' => $validScores->max(),
                    'lowest_score' => $validScores->min(),
                    'pass_rate' => $validScores->filter(function ($score) use ($subject) {
                        return $score >= ($subject->pass_score ?? 60);
                    })->count() / $validScores->count() * 100,
                    'class_averages' => $subjectScores->groupBy('student.class_id')->map(function ($classSubjectScores) {
                        $class = $classSubjectScores->first()->student->class;
                        return [
                            'class_name' => $class->name,
                            'average' => $classSubjectScores->avg('score'),
                        ];
                    })->values()
                ];
            })->values()
        ];

        return response()->json($analysis);
    }

    /**
     * Get grade exam rankings.
     */
    public function getExamRankings(Grade $grade, Exam $exam): JsonResponse
    {
        // 计算年级总分排名
        $studentTotalScores = Score::whereHas('student.class', function ($query) use ($grade) {
                $query->where('grade_id', $grade->id);
            })
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['student.user', 'student.class', 'subject'])
            ->get()
            ->groupBy('student_id')
            ->map(function ($studentScores) {
                $student = $studentScores->first()->student;
                $totalScore = $studentScores->sum('score');
                
                return [
                    'student_id' => $student->id,
                    'student_name' => $student->user->real_name,
                    'student_number' => $student->student_id,
                    'class_name' => $student->class->name,
                    'total_score' => $totalScore,
                    'subject_count' => $studentScores->count(),
                    'average_score' => $totalScore / $studentScores->count(),
                    'subject_scores' => $studentScores->map(function ($score) {
                        return [
                            'subject' => $score->subject->name,
                            'score' => $score->score,
                            'grade_rank' => $score->grade_rank,
                        ];
                    })
                ];
            })
            ->sortByDesc('total_score')
            ->values()
            ->map(function ($student, $index) {
                $student['grade_rank'] = $index + 1;
                return $student;
            });

        // 按班级分组排名
        $classRankings = $studentTotalScores->groupBy('class_name')->map(function ($classStudents, $className) {
            return [
                'class_name' => $className,
                'students' => $classStudents->values(),
                'class_average' => $classStudents->avg('total_score'),
                'top_student' => $classStudents->first(),
            ];
        })->sortByDesc('class_average')->values();

        return response()->json([
            'grade_rankings' => $studentTotalScores->take(50), // 前50名
            'class_rankings' => $classRankings,
            'statistics' => [
                'total_participants' => $studentTotalScores->count(),
                'grade_average' => $studentTotalScores->avg('total_score'),
                'highest_score' => $studentTotalScores->max('total_score'),
                'lowest_score' => $studentTotalScores->min('total_score'),
            ]
        ]);
    }
}
