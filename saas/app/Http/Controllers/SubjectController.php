<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Subject;
use App\Models\Score;
use App\Models\Exam;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects.
     */
    public function index(Request $request): Response
    {
        $query = Subject::query()->orderBy('name');

        // 应用筛选条件
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        if ($request->filled('is_core')) {
            $query->where('is_core', $request->boolean('is_core'));
        }

        $subjects = $query->paginate(20)->withQueryString();

        return Inertia::render('tenant/subjects/Index', [
            'subjects' => $subjects,
            'filters' => $request->only(['search', 'is_core'])
        ]);
    }

    /**
     * Store a newly created subject.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:subjects,name',
            'code' => 'required|string|max:20|unique:subjects,code',
            'is_core' => 'boolean',
            'full_score' => 'required|numeric|min:0',
            'pass_score' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        try {
            $subject = Subject::create($validated);

            return response()->json([
                'message' => '科目创建成功',
                'subject' => $subject
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified subject.
     */
    public function show(Subject $subject): Response
    {
        // 获取科目的统计信息
        $statistics = [
            'total_exams' => $subject->examSubjects()->count(),
            'total_scores' => $subject->scores()->count(),
            'average_score' => $subject->scores()->where('is_absent', false)->avg('score'),
            'pass_rate' => $this->calculatePassRate($subject),
        ];

        return Inertia::render('tenant/subjects/Show', [
            'subject' => $subject,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Update the specified subject.
     */
    public function update(Request $request, Subject $subject): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:subjects,name,' . $subject->id,
            'code' => 'required|string|max:20|unique:subjects,code,' . $subject->id,
            'is_core' => 'boolean',
            'full_score' => 'required|numeric|min:0',
            'pass_score' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        try {
            $subject->update($validated);

            return response()->json([
                'message' => '科目信息更新成功',
                'subject' => $subject
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified subject.
     */
    public function destroy(Subject $subject): JsonResponse
    {
        try {
            // 检查是否有关联的成绩记录
            if ($subject->scores()->count() > 0) {
                return response()->json([
                    'message' => '该科目还有成绩记录，无法删除'
                ], 422);
            }

            // 检查是否有关联的考试科目
            if ($subject->examSubjects()->count() > 0) {
                return response()->json([
                    'message' => '该科目还有考试安排，无法删除'
                ], 422);
            }

            $subject->delete();

            return response()->json([
                'message' => '科目删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subject exam analysis.
     */
    public function getExamAnalysis(Subject $subject, Exam $exam): JsonResponse
    {
        $scores = $subject->scores()
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->with(['student.class.grade'])
            ->get();

        if ($scores->isEmpty()) {
            return response()->json([
                'message' => '该科目在此次考试中暂无成绩数据'
            ]);
        }

        $validScores = $scores->pluck('score');
        
        $analysis = [
            'basic_stats' => [
                'total_students' => $scores->count(),
                'average_score' => $validScores->avg(),
                'highest_score' => $validScores->max(),
                'lowest_score' => $validScores->min(),
                'median_score' => $this->calculateMedian($validScores->toArray()),
                'standard_deviation' => $this->calculateStandardDeviation($validScores->toArray()),
            ],
            'pass_analysis' => [
                'pass_count' => $validScores->filter(fn($s) => $s >= $subject->pass_score)->count(),
                'pass_rate' => $validScores->filter(fn($s) => $s >= $subject->pass_score)->count() / $validScores->count() * 100,
                'excellent_count' => $validScores->filter(fn($s) => $s >= 90)->count(),
                'excellent_rate' => $validScores->filter(fn($s) => $s >= 90)->count() / $validScores->count() * 100,
            ],
            'score_distribution' => [
                '90-100' => $validScores->filter(fn($s) => $s >= 90)->count(),
                '80-89' => $validScores->filter(fn($s) => $s >= 80 && $s < 90)->count(),
                '70-79' => $validScores->filter(fn($s) => $s >= 70 && $s < 80)->count(),
                '60-69' => $validScores->filter(fn($s) => $s >= 60 && $s < 70)->count(),
                '0-59' => $validScores->filter(fn($s) => $s < 60)->count(),
            ],
            'grade_analysis' => $scores->groupBy('student.class.grade_id')->map(function ($gradeScores) {
                $grade = $gradeScores->first()->student->class->grade;
                $gradeValidScores = $gradeScores->pluck('score');
                
                return [
                    'grade_name' => $grade->name,
                    'student_count' => $gradeScores->count(),
                    'average_score' => $gradeValidScores->avg(),
                    'pass_rate' => $gradeValidScores->filter(fn($s) => $s >= $this->subject->pass_score)->count() / $gradeValidScores->count() * 100,
                ];
            })->values(),
            'class_analysis' => $scores->groupBy('student.class_id')->map(function ($classScores) {
                $class = $classScores->first()->student->class;
                $classValidScores = $classScores->pluck('score');
                
                return [
                    'class_name' => $class->name,
                    'grade_name' => $class->grade->name,
                    'student_count' => $classScores->count(),
                    'average_score' => $classValidScores->avg(),
                    'highest_score' => $classValidScores->max(),
                    'lowest_score' => $classValidScores->min(),
                    'pass_rate' => $classValidScores->filter(fn($s) => $s >= $subject->pass_score)->count() / $classValidScores->count() * 100,
                ];
            })->values()
        ];

        return response()->json($analysis);
    }

    /**
     * Get subject exam statistics.
     */
    public function getExamStatistics(Subject $subject, Exam $exam): JsonResponse
    {
        $examSubject = $subject->examSubjects()->where('exam_id', $exam->id)->first();
        
        if (!$examSubject) {
            return response()->json([
                'message' => '该科目未参与此次考试'
            ], 404);
        }

        $scores = $subject->scores()
            ->where('exam_id', $exam->id)
            ->where('is_absent', false)
            ->get();

        $statistics = [
            'exam_info' => [
                'exam_name' => $exam->name,
                'subject_name' => $subject->name,
                'exam_date' => $examSubject->exam_date,
                'full_score' => $examSubject->full_score,
                'pass_score' => $examSubject->pass_score,
            ],
            'participation' => [
                'total_registered' => $subject->scores()->where('exam_id', $exam->id)->count(),
                'actual_participants' => $scores->count(),
                'absent_count' => $subject->scores()->where('exam_id', $exam->id)->where('is_absent', true)->count(),
                'participation_rate' => $scores->count() / $subject->scores()->where('exam_id', $exam->id)->count() * 100,
            ],
            'score_stats' => $scores->isEmpty() ? null : [
                'average' => $scores->avg('score'),
                'median' => $this->calculateMedian($scores->pluck('score')->toArray()),
                'mode' => $this->calculateMode($scores->pluck('score')->toArray()),
                'range' => $scores->max('score') - $scores->min('score'),
                'variance' => $this->calculateVariance($scores->pluck('score')->toArray()),
                'standard_deviation' => $this->calculateStandardDeviation($scores->pluck('score')->toArray()),
            ]
        ];

        return response()->json($statistics);
    }

    /**
     * Get subject difficulty analysis.
     */
    public function getDifficultyAnalysis(Subject $subject): JsonResponse
    {
        $recentExams = $subject->examSubjects()
            ->with('exam')
            ->orderBy('exam_date', 'desc')
            ->limit(10)
            ->get();

        $difficultyAnalysis = $recentExams->map(function ($examSubject) use ($subject) {
            $scores = $subject->scores()
                ->where('exam_id', $examSubject->exam_id)
                ->where('is_absent', false)
                ->pluck('score');

            if ($scores->isEmpty()) {
                return null;
            }

            $average = $scores->avg();
            $passRate = $scores->filter(fn($s) => $s >= $examSubject->pass_score)->count() / $scores->count() * 100;
            
            // 难度系数计算：平均分/满分
            $difficultyCoefficient = $average / $examSubject->full_score;
            
            // 区分度计算（简化版）
            $sortedScores = $scores->sort()->values();
            $topGroup = $sortedScores->slice(-intval($scores->count() * 0.27));
            $bottomGroup = $sortedScores->slice(0, intval($scores->count() * 0.27));
            $discrimination = ($topGroup->avg() - $bottomGroup->avg()) / $examSubject->full_score;

            return [
                'exam_name' => $examSubject->exam->name,
                'exam_date' => $examSubject->exam_date,
                'average_score' => $average,
                'pass_rate' => $passRate,
                'difficulty_coefficient' => $difficultyCoefficient,
                'difficulty_level' => $this->getDifficultyLevel($difficultyCoefficient),
                'discrimination' => $discrimination,
                'discrimination_level' => $this->getDiscriminationLevel($discrimination),
            ];
        })->filter()->values();

        return response()->json([
            'subject_name' => $subject->name,
            'analysis_period' => '最近10次考试',
            'difficulty_analysis' => $difficultyAnalysis,
            'summary' => [
                'average_difficulty' => $difficultyAnalysis->avg('difficulty_coefficient'),
                'average_discrimination' => $difficultyAnalysis->avg('discrimination'),
                'trend_analysis' => $this->analyzeTrend($difficultyAnalysis),
            ]
        ]);
    }

    /**
     * Calculate pass rate for a subject.
     */
    private function calculatePassRate(Subject $subject): float
    {
        $totalScores = $subject->scores()->where('is_absent', false)->count();
        if ($totalScores === 0) return 0;

        $passedScores = $subject->scores()
            ->where('is_absent', false)
            ->where('score', '>=', $subject->pass_score)
            ->count();

        return ($passedScores / $totalScores) * 100;
    }

    /**
     * Calculate median of an array.
     */
    private function calculateMedian(array $numbers): float
    {
        sort($numbers);
        $count = count($numbers);
        
        if ($count === 0) return 0;
        
        if ($count % 2 === 0) {
            return ($numbers[$count / 2 - 1] + $numbers[$count / 2]) / 2;
        } else {
            return $numbers[intval($count / 2)];
        }
    }

    /**
     * Calculate mode of an array.
     */
    private function calculateMode(array $numbers): float
    {
        if (empty($numbers)) return 0;
        
        $frequency = array_count_values($numbers);
        $maxFreq = max($frequency);
        $modes = array_keys($frequency, $maxFreq);
        
        return $modes[0];
    }

    /**
     * Calculate variance of an array.
     */
    private function calculateVariance(array $numbers): float
    {
        if (count($numbers) === 0) return 0;
        
        $mean = array_sum($numbers) / count($numbers);
        $squaredDiffs = array_map(fn($x) => pow($x - $mean, 2), $numbers);
        
        return array_sum($squaredDiffs) / count($numbers);
    }

    /**
     * Calculate standard deviation of an array.
     */
    private function calculateStandardDeviation(array $numbers): float
    {
        return sqrt($this->calculateVariance($numbers));
    }

    /**
     * Get difficulty level description.
     */
    private function getDifficultyLevel(float $coefficient): string
    {
        if ($coefficient >= 0.8) return '容易';
        if ($coefficient >= 0.6) return '中等';
        if ($coefficient >= 0.4) return '较难';
        return '困难';
    }

    /**
     * Get discrimination level description.
     */
    private function getDiscriminationLevel(float $discrimination): string
    {
        if ($discrimination >= 0.4) return '很好';
        if ($discrimination >= 0.3) return '良好';
        if ($discrimination >= 0.2) return '尚可';
        return '需改进';
    }

    /**
     * Analyze trend of difficulty.
     */
    private function analyzeTrend($data): string
    {
        if ($data->count() < 2) return '数据不足';
        
        $recent = $data->take(3)->avg('difficulty_coefficient');
        $earlier = $data->skip(3)->take(3)->avg('difficulty_coefficient');
        
        if ($recent > $earlier + 0.1) return '难度下降';
        if ($recent < $earlier - 0.1) return '难度上升';
        return '难度稳定';
    }
}
