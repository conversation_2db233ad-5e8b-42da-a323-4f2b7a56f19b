<?php

namespace App\Traits;

trait TenantModuleAccess
{
    /**
     * Check if tenant has access to a specific module.
     */
    public function hasModuleAccess(string $module): bool
    {
        // Core module is always accessible
        if ($module === 'core') {
            return true;
        }

        // Get available modules from cache or subscription
        $availableModules = $this->getAvailableModules();

        return in_array($module, $availableModules);
    }

    /**
     * Get all modules available to this tenant.
     */
    public function getAvailableModules(): array
    {
        $cacheKey = "tenant_{$this->id}_modules";

        return cache()->remember($cacheKey, now()->addHours(1), function () {
            // Core module is always available
            $modules = ['core'];

            // If tenant is on trial, all modules are available
            if ($this->onTrial()) {
                return array_keys(config('modules', []));
            }

            // Check if tenant has an active subscription
            if ($subscription = $this->subscription('default')) {
                // Get module access from subscription
                $moduleAccess = $subscription->module_access ?? [];

                if (is_array($moduleAccess)) {
                    $modules = array_merge($modules, $moduleAccess);
                } elseif (is_string($moduleAccess)) {
                    // Handle JSON string
                    $decodedModules = json_decode($moduleAccess, true);
                    if (is_array($decodedModules)) {
                        $modules = array_merge($modules, $decodedModules);
                    }
                }
            }

            return array_unique($modules);
        });
    }

    /**
     * Get the subscribed modules for this tenant.
     * Alias for getAvailableModules for backward compatibility.
     */
    public function getSubscribedModules(): array
    {
        return $this->getAvailableModules();
    }

    /**
     * Check if tenant has access to a specific module.
     * Alias for hasModuleAccess for backward compatibility.
     */
    public function hasModule(string $module): bool
    {
        return $this->hasModuleAccess($module);
    }

    /**
     * Subscribe tenant to specified modules.
     */
    public function subscribeToModules($modules): void
    {
        // Convert single module to array
        if (!is_array($modules)) {
            $modules = [$modules];
        }

        // Get current subscription
        $subscription = $this->subscription('default');

        if (!$subscription) {
            // No subscription, can't add modules
            return;
        }

        // Get current modules
        $currentModules = $subscription->module_access ?? [];
        if (is_string($currentModules)) {
            $currentModules = json_decode($currentModules, true) ?? [];
        }
        if (!is_array($currentModules)) {
            $currentModules = [];
        }

        // Add new modules
        $newModules = array_unique(array_merge($currentModules, $modules));

        // Update subscription
        $subscription->module_access = $newModules;
        $subscription->save();

        // Clear cache
        cache()->forget("tenant_{$this->id}_modules");
    }

    /**
     * Unsubscribe tenant from specified modules.
     */
    public function unsubscribeFromModules($modules): void
    {
        // Convert single module to array
        if (!is_array($modules)) {
            $modules = [$modules];
        }

        // Don't allow unsubscribing from core module
        $modules = array_filter($modules, function($module) {
            return $module !== 'core';
        });

        // Get current subscription
        $subscription = $this->subscription('default');

        if (!$subscription) {
            // No subscription, nothing to remove
            return;
        }

        // Get current modules
        $currentModules = $subscription->module_access ?? [];
        if (is_string($currentModules)) {
            $currentModules = json_decode($currentModules, true) ?? [];
        }
        if (!is_array($currentModules)) {
            $currentModules = [];
        }

        // Remove modules
        $newModules = array_values(array_diff($currentModules, $modules));

        // Update subscription
        $subscription->module_access = $newModules;
        $subscription->save();

        // Clear cache
        cache()->forget("tenant_{$this->id}_modules");
    }

    /**
     * Get the current subscription plan name.
     */
    public function getCurrentPlanName(): ?string
    {
        // Check if tenant is on trial
        if ($this->onTrial()) {
            return '试用期';
        }

        // Check if tenant has an active subscription
        if ($subscription = $this->subscription('default')) {
            $plans = config('saas.plan_modules', []);
            return $plans[$subscription->stripe_price]['name'] ?? null;
        }

        // No subscription
        return '无订阅';
    }
}
