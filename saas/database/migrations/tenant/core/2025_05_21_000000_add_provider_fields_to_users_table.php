<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Provider相关字段
            $table->string('provider')->nullable()->after('password');
            $table->string('provider_id')->nullable()->after('provider');

            // 用户基本信息字段
            $table->string('username', 50)->unique()->after('name');
            $table->string('real_name')->nullable()->after('username');
            $table->string('phone', 20)->nullable()->after('email');
            $table->string('avatar_url')->nullable()->after('phone');

            // 状态和登录字段
            $table->boolean('is_active')->default(true)->after('remember_token');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'provider',
                'provider_id',
                'username',
                'real_name',
                'phone',
                'avatar_url',
                'is_active',
                'last_login_at'
            ]);
        });
    }
};
