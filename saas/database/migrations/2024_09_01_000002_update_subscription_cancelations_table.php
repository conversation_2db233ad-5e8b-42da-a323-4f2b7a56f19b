<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_cancelations', function (Blueprint $table) {
            $table->string('feedback')->nullable()->after('reason');
            $table->boolean('want_to_return')->default(false)->after('feedback');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_cancelations', function (Blueprint $table) {
            $table->dropColumn(['feedback', 'want_to_return']);
        });
    }
};