<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Hash;

class TenantDatabaseSeeder extends Seeder
{
    /**
     * Seed the tenant's database.
     */
    public function run(): void
    {
        // Get tenant and ensure it has demo modules access
        $tenant = tenant();

        // For demo purposes, ensure tenant has access to all modules
        // Set trial period if not already set
        if (!$tenant->onTrial() && !$tenant->subscribed('default')) {
            $tenant->trial_ends_at = now()->addDays(30);
            $tenant->save();
        }

        // For demo, we'll assume all modules are available
        $subscribedModules = ['core', 'score', 'salary'];

        // Default admin user - use tenant-specific admin email
        $email = $tenant->email ?? 'admin@' . $tenant->id . '.local';

        // Check if user already exists and skip creation
        $admin = User::where('email', $email)->first();

        if (!$admin) {
            $admin = User::create([
                'name' => '系统管理员',
                'username' => 'admin',
                'real_name' => '系统管理员',
                'email' => $email,
                'password' => Hash::make('password'),
                'is_active' => true,
                'email_verified_at' => now(),
            ]);
        }

        // Create default roles with realistic high school roles
        $roles = [
            ['name' => 'super_admin', 'display_name' => '超级管理员', 'description' => '系统最高权限管理员'],
            ['name' => 'principal', 'display_name' => '校长', 'description' => '学校校长'],
            ['name' => 'academic_director', 'display_name' => '教务主任', 'description' => '教务处主任'],
            ['name' => 'student_affairs_director', 'display_name' => '德育主任', 'description' => '德育处主任'],
            ['name' => 'grade_director', 'display_name' => '年级主任', 'description' => '年级组长'],
            ['name' => 'class_teacher', 'display_name' => '班主任', 'description' => '班级管理教师'],
            ['name' => 'subject_teacher', 'display_name' => '任课教师', 'description' => '学科教学教师'],
            ['name' => 'student', 'display_name' => '学生', 'description' => '在校学生'],
            ['name' => 'parent', 'display_name' => '家长', 'description' => '学生家长'],
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(
                ['name' => $role['name']],
                $role
            );
        }

        // Assign admin to super admin role if not already assigned
        $superAdminRole = Role::where('name', 'super_admin')->first();
        if (!$admin->roles()->where('role_id', $superAdminRole->id)->exists()) {
            $admin->roles()->attach($superAdminRole->id);
        }

        // Core permissions - always included with realistic high school permissions
        $permissions = [
            // 系统管理权限
            ['name' => '用户管理', 'slug' => 'manage-users', 'module' => 'core'],
            ['name' => '角色管理', 'slug' => 'manage-roles', 'module' => 'core'],
            ['name' => '权限管理', 'slug' => 'manage-permissions', 'module' => 'core'],
            ['name' => '系统设置', 'slug' => 'manage-settings', 'module' => 'core'],

            // 教务管理权限
            ['name' => '部门管理', 'slug' => 'manage-departments', 'module' => 'core'],
            ['name' => '年级管理', 'slug' => 'manage-grades', 'module' => 'core'],
            ['name' => '班级管理', 'slug' => 'manage-classes', 'module' => 'core'],
            ['name' => '学科管理', 'slug' => 'manage-subjects', 'module' => 'core'],
            ['name' => '教师管理', 'slug' => 'manage-teachers', 'module' => 'core'],
            ['name' => '学生管理', 'slug' => 'manage-students', 'module' => 'core'],

            // 文章/公告管理权限
            ['name' => '查看文章', 'slug' => 'view-posts', 'module' => 'core'],
            ['name' => '创建文章', 'slug' => 'create-posts', 'module' => 'core'],
            ['name' => '编辑文章', 'slug' => 'edit-posts', 'module' => 'core'],
            ['name' => '删除文章', 'slug' => 'delete-posts', 'module' => 'core'],
            ['name' => '发布文章', 'slug' => 'publish-posts', 'module' => 'core'],
        ];

        // Only add score permissions if the tenant has the score module
        if (in_array('score', $subscribedModules)) {
            $scorePermissions = [
                ['name' => '考试管理', 'slug' => 'manage-exams', 'module' => 'score'],
                ['name' => '成绩录入', 'slug' => 'enter-scores', 'module' => 'score'],
                ['name' => '成绩查询', 'slug' => 'view-scores', 'module' => 'score'],
                ['name' => '成绩分析', 'slug' => 'analyze-scores', 'module' => 'score'],
                ['name' => '成绩导入', 'slug' => 'import-scores', 'module' => 'score'],
                ['name' => '成绩导出', 'slug' => 'export-scores', 'module' => 'score'],
                ['name' => '排名统计', 'slug' => 'rank-statistics', 'module' => 'score'],
                ['name' => '报表生成', 'slug' => 'generate-reports', 'module' => 'score'],
            ];
            $permissions = array_merge($permissions, $scorePermissions);
        }

        // Only add salary permissions if the tenant has the salary module
        if (in_array('salary', $subscribedModules)) {
            $salaryPermissions = [
                ['name' => '工资结构管理', 'slug' => 'manage-salary-structure', 'module' => 'salary'],
                ['name' => '工资录入', 'slug' => 'enter-salaries', 'module' => 'salary'],
                ['name' => '工资查询', 'slug' => 'view-salaries', 'module' => 'salary'],
                ['name' => '工资计算', 'slug' => 'calculate-salaries', 'module' => 'salary'],
                ['name' => '工资审批', 'slug' => 'approve-salaries', 'module' => 'salary'],
                ['name' => '工资发放', 'slug' => 'distribute-salaries', 'module' => 'salary'],
                ['name' => '工资单生成', 'slug' => 'generate-payslips', 'module' => 'salary'],
                ['name' => '薪资统计', 'slug' => 'salary-statistics', 'module' => 'salary'],
            ];
            $permissions = array_merge($permissions, $salaryPermissions);
        }

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        // Create realistic high school demo data
        $this->call(HighSchoolDemoSeeder::class);
    }

    /**
     * Assign permissions to roles based on realistic high school hierarchy
     */
    protected function assignPermissionsToRoles(): void
    {
        // 超级管理员拥有所有权限
        $superAdmin = Role::where('name', 'super_admin')->first();
        if ($superAdmin && $superAdmin->permissions()->count() === 0) {
            $superAdmin->permissions()->attach(Permission::all()->pluck('id'));
        }

        // 校长拥有除系统管理外的所有权限
        $principal = Role::where('name', 'principal')->first();
        if ($principal && $principal->permissions()->count() === 0) {
            $principal->permissions()->attach(
                Permission::whereNotIn('slug', ['manage-roles', 'manage-permissions', 'manage-settings'])->pluck('id')
            );
        }

        // 教务主任拥有教务相关权限
        $academicDirector = Role::where('name', 'academic_director')->first();
        if ($academicDirector && $academicDirector->permissions()->count() === 0) {
            $academicDirector->permissions()->attach(
                Permission::whereIn('slug', [
                    'manage-departments', 'manage-grades', 'manage-classes', 'manage-subjects',
                    'manage-teachers', 'manage-students', 'manage-exams', 'view-scores', 'analyze-scores',
                    'view-posts', 'create-posts', 'edit-posts', 'publish-posts'
                ])->pluck('id')
            );
        }

        // 年级主任拥有年级管理权限
        $gradeDirector = Role::where('name', 'grade_director')->first();
        if ($gradeDirector && $gradeDirector->permissions()->count() === 0) {
            $gradeDirector->permissions()->attach(
                Permission::whereIn('slug', [
                    'manage-classes', 'manage-teachers', 'manage-students', 'view-scores'
                ])->pluck('id')
            );
        }

        // 班主任拥有班级管理权限
        $classTeacher = Role::where('name', 'class_teacher')->first();
        if ($classTeacher && $classTeacher->permissions()->count() === 0) {
            $classTeacher->permissions()->attach(
                Permission::whereIn('slug', ['manage-students', 'view-scores'])->pluck('id')
            );
        }

        // 任课教师拥有成绩相关权限
        $teacher = Role::where('name', 'subject_teacher')->first();
        if ($teacher && $teacher->permissions()->count() === 0) {
            $teacher->permissions()->attach(
                Permission::whereIn('slug', ['enter-scores', 'view-scores', 'view-posts'])->pluck('id')
            );
        }
    }
}
