<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\Domain;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Setting up tenants and domains...');

        // 创建或更新默认租户
        $tenant = $this->createOrUpdateTenant([
            'id' => 'default',
            'email' => '<EMAIL>',
            'data' => [
                'name' => 'ScoreDB25 Default Tenant',
                'email' => '<EMAIL>',
            ]
        ]);

        // 为租户创建或更新主域名
        $this->createOrUpdateDomain([
            'domain' => 'default.scoredb25.ddev.site',
            'tenant_id' => $tenant->id,
            'is_primary' => 1,
            'is_fallback' => 0,
        ]);

        // 创建或更新测试租户
        $testTenant = $this->createOrUpdateTenant([
            'id' => 'test',
            'email' => '<EMAIL>',
            'data' => [
                'name' => 'Test Tenant',
                'email' => '<EMAIL>',
            ]
        ]);

        // 为测试租户创建或更新域名
        $this->createOrUpdateDomain([
            'domain' => 'test.scoredb25.ddev.site',
            'tenant_id' => $testTenant->id,
            'is_primary' => 1,
            'is_fallback' => 0,
        ]);

        $this->command->info('Created tenants and domains successfully:');
        $this->command->info('- Default tenant: default.scoredb25.ddev.site (<EMAIL>)');
        $this->command->info('- Test tenant: test.scoredb25.ddev.site (<EMAIL>)');
        $this->command->info('');
        $this->command->info('🔒 Security: Admin login at hiddenadmin.scoredb25.ddev.site/login');
        $this->command->info('🏢 Tenants: Each tenant has their own subdomain for direct login');
    }

    /**
     * 创建或更新租户
     */
    protected function createOrUpdateTenant(array $data): Tenant
    {
        $tenant = Tenant::find($data['id']);

        if ($tenant) {
            $this->command->info("Tenant '{$data['id']}' already exists, updating...");
            $tenant->update($data);
        } else {
            $this->command->info("Creating new tenant '{$data['id']}'...");
            try {
                // 检查并删除可能存在的孤立数据库
                $databaseName = 'tenant' . $data['id'];
                try {
                    DB::statement("DROP DATABASE IF EXISTS {$databaseName}");
                    $this->command->info("Cleaned up existing database: {$databaseName}");
                } catch (\Exception $e) {
                    $this->command->warn("Could not clean database {$databaseName}: " . $e->getMessage());
                }

                $tenant = Tenant::create($data);
                $this->command->info("Successfully created tenant '{$data['id']}'");
            } catch (\Exception $e) {
                $this->command->error("Failed to create tenant '{$data['id']}': " . $e->getMessage());
                throw $e;
            }
        }

        return $tenant;
    }

    /**
     * 创建或更新域名
     */
    protected function createOrUpdateDomain(array $data): Domain
    {
        $domain = Domain::where('domain', $data['domain'])->first();

        if ($domain) {
            $this->command->info("Domain '{$data['domain']}' already exists, updating...");
            $domain->update($data);
        } else {
            $this->command->info("Creating new domain '{$data['domain']}'...");
            $domain = Domain::create($data);
            $this->command->info("Successfully created domain '{$data['domain']}'");
        }

        return $domain;
    }
}
