<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Classes;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\Exam;
use App\Models\ExamSubject;
use App\Models\Score;
use App\Models\Department;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class HighSchoolDemoSeeder extends Seeder
{
    private $faker;

    /**
     * 创建真实的高中演示数据
     */
    public function run(): void
    {
        $this->faker = Faker::create('zh_CN');

        $this->command->info('🏫 开始创建高中演示数据...');
        $startTime = microtime(true);

        // 检查是否已经有数据，如果有则清理
        $this->cleanExistingData();

        // 将大事务拆分成多个小事务，避免锁耗尽
        $this->command->info('📊 创建部门数据...');
        $this->createDepartments();

        $this->command->info('🎓 创建年级数据...');
        $this->createGrades();

        $this->command->info('🏛️ 创建班级数据...');
        $this->createClasses();

        $this->command->info('📚 创建科目数据...');
        $this->createSubjects();

        $this->command->info('👨‍🏫 创建教师数据...');
        $this->createTeachers();

        $this->command->info('👨‍🎓 创建学生数据（这可能需要一些时间）...');
        $this->createStudents();

        $this->command->info('👨‍👩‍👧‍👦 创建家长数据...');
        $this->createParents();

        $this->command->info('📝 创建考试数据...');
        $this->createExams();

        $this->command->info('📊 创建成绩数据（这可能需要较长时间）...');
        $this->createScores();

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        $this->command->info("✅ 高中演示数据创建完成！耗时: {$duration}秒");
    }

    /**
     * 清理现有数据
     */
    protected function cleanExistingData(): void
    {
        $this->command->info('Cleaning existing tenant data...');

        // PostgreSQL中按依赖关系顺序删除数据，或者使用CASCADE
        $tables = [
            'scores',
            'exam_subjects',
            'exams',
            'students',
            'teachers',
            'classes',
            'grades',
            'subjects',
            'departments',
            'users',
            'roles',
            'permissions',
            'role_user',
            'permission_role'
        ];

        foreach ($tables as $table) {
            try {
                // 检查表是否存在
                $exists = DB::select("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", [$table]);
                if ($exists[0]->exists) {
                    // 使用DELETE而不是TRUNCATE来避免外键约束问题
                    DB::table($table)->delete();
                    $this->command->info("Cleaned table: {$table}");
                }
            } catch (\Exception $e) {
                $this->command->warn("Could not clean table {$table}: " . $e->getMessage());
            }
        }

        $this->command->info('Existing data cleaned successfully.');
    }

    /**
     * 创建部门
     */
    protected function createDepartments(): void
    {
        $departments = [
            ['name' => '校长办公室', 'description' => '学校行政管理'],
            ['name' => '教务处', 'description' => '教学事务管理'],
            ['name' => '德育处', 'description' => '学生德育管理'],
            ['name' => '总务处', 'description' => '后勤保障管理'],
            ['name' => '高一年级组', 'description' => '高一年级教学管理'],
            ['name' => '高二年级组', 'description' => '高二年级教学管理'],
            ['name' => '高三年级组', 'description' => '高三年级教学管理'],
        ];

        foreach ($departments as $dept) {
            Department::firstOrCreate(['name' => $dept['name']], $dept);
        }
    }

    /**
     * 创建年级
     */
    protected function createGrades(): void
    {
        $grades = [
            ['name' => '高一年级', 'level' => 1, 'academic_year' => '2024-2025'],
            ['name' => '高二年级', 'level' => 2, 'academic_year' => '2023-2024'],
            ['name' => '高三年级', 'level' => 3, 'academic_year' => '2022-2023'],
        ];

        foreach ($grades as $grade) {
            Grade::firstOrCreate(['name' => $grade['name']], $grade);
        }
    }

    /**
     * 创建班级
     */
    protected function createClasses(): void
    {
        $grades = Grade::all();

        foreach ($grades as $grade) {
            for ($i = 1; $i <= 6; $i++) { // 每个年级6个班
                Classes::firstOrCreate([
                    'name' => $grade->name . $i . '班',
                    'grade_id' => $grade->id,
                ], [
                    'name' => $grade->name . $i . '班',
                    'grade_id' => $grade->id,
                    'academic_year' => $grade->academic_year,
                    'capacity' => 45,
                ]);
            }
        }
    }

    /**
     * 创建科目
     */
    protected function createSubjects(): void
    {
        $subjects = [
            ['name' => '语文', 'code' => 'CHI', 'is_core' => true, 'full_score' => 150, 'pass_score' => 90],
            ['name' => '数学', 'code' => 'MATH', 'is_core' => true, 'full_score' => 150, 'pass_score' => 90],
            ['name' => '英语', 'code' => 'ENG', 'is_core' => true, 'full_score' => 150, 'pass_score' => 90],
            ['name' => '物理', 'code' => 'PHY', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
            ['name' => '化学', 'code' => 'CHEM', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
            ['name' => '生物', 'code' => 'BIO', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
            ['name' => '政治', 'code' => 'POL', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
            ['name' => '历史', 'code' => 'HIS', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
            ['name' => '地理', 'code' => 'GEO', 'is_core' => false, 'full_score' => 100, 'pass_score' => 60],
        ];

        foreach ($subjects as $subject) {
            Subject::firstOrCreate(['code' => $subject['code']], $subject);
        }
    }

    /**
     * 创建教师用户
     */
    protected function createTeachers(): void
    {
        $departments = Department::all()->keyBy('name');
        $subjects = Subject::all()->keyBy('name');

        // 管理层教师
        $adminTeachers = [
            ['name' => '张校长', 'subject' => null, 'role' => '校长', 'dept' => '校长办公室', 'position' => '校长'],
            ['name' => '李教务主任', 'subject' => '数学', 'role' => '教务主任', 'dept' => '教务处', 'position' => '教务主任'],
            ['name' => '王德育主任', 'subject' => '政治', 'role' => '德育主任', 'dept' => '德育处', 'position' => '德育主任'],
            ['name' => '赵高一主任', 'subject' => '语文', 'role' => '年级主任', 'dept' => '高一年级组', 'position' => '年级主任'],
            ['name' => '钱高二主任', 'subject' => '英语', 'role' => '年级主任', 'dept' => '高二年级组', 'position' => '年级主任'],
            ['name' => '孙高三主任', 'subject' => '数学', 'role' => '年级主任', 'dept' => '高三年级组', 'position' => '年级主任'],
        ];

        foreach ($adminTeachers as $teacherData) {
            $this->createTeacher($teacherData, $departments, $subjects);
        }

        // 为每个科目创建多名教师
        $subjectTeachers = [
            '语文' => ['陈语文', '刘语文', '张语文', '王语文', '李语文'],
            '数学' => ['周数学', '吴数学', '郑数学', '冯数学', '卫数学'],
            '英语' => ['蒋英语', '韩英语', '杨英语', '朱英语', '许英语'],
            '物理' => ['何物理', '高物理', '林物理', '罗物理', '郭物理'],
            '化学' => ['马化学', '孙化学', '胡化学', '梁化学', '谢化学'],
            '生物' => ['唐生物', '宋生物', '薛生物', '范生物', '邓生物'],
            '政治' => ['彭政治', '曾政治', '萧政治', '田政治', '董政治'],
            '历史' => ['潘历史', '袁历史', '于历史', '蒋历史', '蔡历史'],
            '地理' => ['余地理', '叶地理', '杜地理', '程地理', '魏地理'],
        ];

        $deptMapping = [
            '语文' => ['高一年级组', '高二年级组', '高三年级组'],
            '数学' => ['高一年级组', '高二年级组', '高三年级组'],
            '英语' => ['高一年级组', '高二年级组', '高三年级组'],
            '物理' => ['高二年级组', '高三年级组'],
            '化学' => ['高二年级组', '高三年级组'],
            '生物' => ['高二年级组', '高三年级组'],
            '政治' => ['高一年级组', '高二年级组', '高三年级组'],
            '历史' => ['高一年级组', '高二年级组', '高三年级组'],
            '地理' => ['高一年级组', '高二年级组', '高三年级组'],
        ];

        foreach ($subjectTeachers as $subjectName => $teachers) {
            $depts = $deptMapping[$subjectName];
            foreach ($teachers as $index => $teacherName) {
                $dept = $depts[$index % count($depts)];
                $teacherData = [
                    'name' => $teacherName,
                    'subject' => $subjectName,
                    'role' => '任课教师',
                    'dept' => $dept,
                    'position' => '任课教师'
                ];
                $this->createTeacher($teacherData, $departments, $subjects);
            }
        }

        // 为每个班级分配班主任
        $this->assignClassTeachers();
    }

    /**
     * 创建单个教师
     */
    protected function createTeacher(array $teacherData, $departments, $subjects): void
    {
        $email = $this->generateEmail($teacherData['name']);
        $teacherId = 'T' . str_pad((string) rand(1000, 9999), 4, '0', STR_PAD_LEFT);

        $user = User::firstOrCreate(['email' => $email], [
            'name' => $teacherData['name'],
            'username' => $this->generateUsername($teacherData['name']),
            'real_name' => $teacherData['name'],
            'email' => $email,
            'password' => Hash::make('password'),
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // 创建教师记录
        $department = $departments->get($teacherData['dept']);
        Teacher::firstOrCreate(['user_id' => $user->id], [
            'user_id' => $user->id,
            'teacher_id' => $teacherId,
            'department_id' => $department ? $department->id : null,
            'position' => $teacherData['position'],
            'hire_date' => $this->faker->dateTimeBetween('-10 years', '-1 year'),
        ]);

        // 分配角色
        $role = Role::where('name', $teacherData['role'])->first();
        if ($role && !$user->roles()->where('role_id', $role->id)->exists()) {
            $user->roles()->attach($role->id);
        }
    }

    /**
     * 为班级分配班主任
     */
    protected function assignClassTeachers(): void
    {
        $classes = Classes::all();
        $teachers = Teacher::whereHas('user.roles', function($query) {
            $query->where('name', 'subject_teacher');
        })->get();

        foreach ($classes as $class) {
            if (!$class->class_teacher_id && $teachers->isNotEmpty()) {
                $teacher = $teachers->random();
                $class->update(['class_teacher_id' => $teacher->id]);

                // 为班主任添加班主任角色
                $classTeacherRole = Role::where('name', 'class_teacher')->first();
                if ($classTeacherRole && !$teacher->user->roles()->where('role_id', $classTeacherRole->id)->exists()) {
                    $teacher->user->roles()->attach($classTeacherRole->id);
                }

                // 移除已分配的教师，避免重复分配
                $teachers = $teachers->reject(function($t) use ($teacher) {
                    return $t->id === $teacher->id;
                });
            }
        }
    }

    /**
     * 创建学生用户
     */
    protected function createStudents(): void
    {
        $classes = Classes::all();
        $studentRole = Role::where('name', '学生')->first();

        $totalClasses = $classes->count();
        $currentClass = 0;

        foreach ($classes as $class) {
            $currentClass++;
            $studentsPerClass = rand(40, 45); // 每班40-45名学生

            $this->command->info("  创建 {$class->name} 学生数据 ({$currentClass}/{$totalClasses})...");

            for ($i = 1; $i <= $studentsPerClass; $i++) {
                $gender = rand(0, 1) ? 'male' : 'female';
                $name = $this->faker->name($gender);

                // 生成唯一的学号：年份 + 年级 + 班级 + 序号
                $studentId = sprintf('%d%02d%02d%03d',
                    2024 - $class->grade->level + 1,
                    $class->grade->level,
                    $class->id, // 使用班级ID确保唯一性
                    $i
                );

                $email = $this->generateEmail($name, 'student');

                $user = User::firstOrCreate(['email' => $email], [
                    'name' => $name,
                    'username' => $studentId,
                    'real_name' => $name,
                    'email' => $email,
                    'password' => Hash::make('password'),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                // 创建学生记录
                Student::firstOrCreate(['user_id' => $user->id], [
                    'user_id' => $user->id,
                    'student_id' => $studentId,
                    'class_id' => $class->id,
                    'gender' => $gender,
                    'birth_date' => $this->faker->dateTimeBetween('-18 years', '-15 years'),
                    'admission_date' => $this->faker->dateTimeBetween('-3 years', '-1 year'),
                    'address' => $this->faker->address,
                    'parent_name' => $this->faker->name,
                    'parent_phone' => $this->faker->phoneNumber,
                ]);

                // 分配学生角色
                if ($studentRole && !$user->roles()->where('role_id', $studentRole->id)->exists()) {
                    $user->roles()->attach($studentRole->id);
                }
            }
        }

        $totalStudents = Student::count();
        $this->command->info("  ✅ 共创建了 {$totalStudents} 名学生");
    }

    /**
     * 创建家长用户
     */
    protected function createParents(): void
    {
        $students = Student::with('user')->get();
        $parentRole = Role::where('name', '家长')->first();

        foreach ($students as $student) {
            // 为每个学生创建1-2个家长
            $parentCount = rand(1, 2);

            for ($i = 1; $i <= $parentCount; $i++) {
                $parentName = $student->parent_name ?: $this->faker->name;
                $relation = $i === 1 ? '父亲' : '母亲';

                $email = $this->generateEmail($parentName . $relation, 'parent');

                $user = User::firstOrCreate(['email' => $email], [
                    'name' => $parentName,
                    'username' => 'P' . $student->student_id . $i,
                    'real_name' => $parentName,
                    'email' => $email,
                    'password' => Hash::make('password'),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                // 分配家长角色
                if ($parentRole && !$user->roles()->where('role_id', $parentRole->id)->exists()) {
                    $user->roles()->attach($parentRole->id);
                }
            }
        }
    }

    /**
     * 创建考试数据
     */
    protected function createExams(): void
    {
        $subjects = Subject::all();
        $grades = Grade::all();

        $examTypes = [
            ['name' => '月考一', 'type' => 'monthly', 'month' => 3],
            ['name' => '期中考试', 'type' => 'midterm', 'month' => 5],
            ['name' => '月考二', 'type' => 'monthly', 'month' => 6],
            ['name' => '期末考试', 'type' => 'final', 'month' => 7],
            ['name' => '月考三', 'type' => 'monthly', 'month' => 10],
            ['name' => '期中考试', 'type' => 'midterm', 'month' => 11],
            ['name' => '月考四', 'type' => 'monthly', 'month' => 12],
            ['name' => '期末考试', 'type' => 'final', 'month' => 1],
        ];

        foreach ($grades as $grade) {
            foreach ($examTypes as $examType) {
                $examDate = now()->setMonth($examType['month'])->setDay(15);

                $exam = Exam::firstOrCreate([
                    'name' => $grade->name . $examType['name'],
                    'academic_year' => $grade->academic_year,
                ], [
                    'name' => $grade->name . $examType['name'],
                    'exam_type' => $examType['type'],
                    'start_date' => $examDate,
                    'end_date' => $examDate->copy()->addDays(3),
                    'academic_year' => $grade->academic_year,
                    'semester' => $examType['month'] <= 7 ? 'spring' : 'autumn',
                    'is_published' => true,
                ]);

                // 为考试添加科目
                $examSubjects = $this->getSubjectsForGrade($subjects, $grade->level);
                foreach ($examSubjects as $subject) {
                    ExamSubject::firstOrCreate([
                        'exam_id' => $exam->id,
                        'subject_id' => $subject->id,
                    ], [
                        'exam_id' => $exam->id,
                        'subject_id' => $subject->id,
                        'exam_date' => $examDate->copy()->addDays(rand(0, 2)),
                        'start_time' => '09:00:00',
                        'end_time' => '11:30:00',
                        'full_score' => $subject->full_score,
                        'pass_score' => $subject->pass_score,
                        'weight' => 1.0,
                    ]);
                }
            }
        }
    }

    /**
     * 获取年级对应的科目
     */
    protected function getSubjectsForGrade($subjects, $gradeLevel): array
    {
        $coreSubjects = $subjects->where('is_core', true)->all();

        if ($gradeLevel === 1) {
            // 高一：语数英 + 政史地生物化
            return array_merge($coreSubjects, $subjects->whereIn('code', ['POL', 'HIS', 'GEO', 'PHY', 'CHEM', 'BIO'])->all());
        } elseif ($gradeLevel === 2) {
            // 高二：语数英 + 选科（这里简化为物化生）
            return array_merge($coreSubjects, $subjects->whereIn('code', ['PHY', 'CHEM', 'BIO'])->all());
        } else {
            // 高三：语数英 + 选科
            return array_merge($coreSubjects, $subjects->whereIn('code', ['PHY', 'CHEM', 'BIO'])->all());
        }
    }

    /**
     * 创建成绩数据
     */
    protected function createScores(): void
    {
        $exams = Exam::with(['examSubjects.subject'])->get();
        $students = Student::with('class.grade')->get();

        // 获取第一个管理员用户作为创建者
        $adminUser = User::first();
        if (!$adminUser) {
            $this->command->error('没有找到管理员用户，无法创建成绩数据');
            return;
        }

        $totalExams = $exams->count();
        $currentExam = 0;

        foreach ($exams as $exam) {
            $currentExam++;
            $this->command->info("  创建 {$exam->name} 成绩数据 ({$currentExam}/{$totalExams})...");

            // 获取参加此考试的学生（根据年级筛选）
            $examStudents = $students->filter(function($student) use ($exam) {
                return $student->class && $student->class->grade &&
                       str_contains($exam->name, $student->class->grade->name);
            });

            // 使用小事务批量处理成绩数据
            DB::transaction(function () use ($exam, $examStudents, $adminUser) {
                $batchSize = 100; // 每批处理100条记录
                $scoreData = [];

                foreach ($exam->examSubjects as $examSubject) {
                    foreach ($examStudents as $student) {
                        // 检查是否已存在
                        $existingScore = Score::where([
                            'exam_id' => $exam->id,
                            'student_id' => $student->id,
                            'subject_id' => $examSubject->subject_id,
                        ])->first();

                        if ($existingScore) {
                            continue; // 跳过已存在的记录
                        }

                        // 10% 概率缺考
                        $isAbsent = rand(1, 100) <= 10;
                        $score = $isAbsent ? 0 : $this->generateNormalScore($examSubject->full_score);

                        $scoreData[] = [
                            'exam_id' => $exam->id,
                            'student_id' => $student->id,
                            'subject_id' => $examSubject->subject_id,
                            'score' => $score,
                            'is_absent' => $isAbsent,
                            'is_makeup' => false,
                            'created_by' => $adminUser->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];

                        // 批量插入
                        if (count($scoreData) >= $batchSize) {
                            Score::insert($scoreData);
                            $scoreData = [];
                        }
                    }
                }

                // 插入剩余数据
                if (!empty($scoreData)) {
                    Score::insert($scoreData);
                }
            });

            // 计算排名
            $this->calculateRankings($exam);
        }
    }

    /**
     * 生成正态分布的成绩
     */
    protected function generateNormalScore(float $fullScore): float
    {
        // 使用Box-Muller变换生成正态分布
        $mean = $fullScore * 0.75; // 平均分为满分的75%
        $stdDev = $fullScore * 0.15; // 标准差为满分的15%

        $u1 = mt_rand() / mt_getrandmax();
        $u2 = mt_rand() / mt_getrandmax();

        $z = sqrt(-2 * log($u1)) * cos(2 * pi() * $u2);
        $score = $mean + $stdDev * $z;

        // 确保分数在合理范围内
        $score = max(0, min($fullScore, $score));

        return round($score, 1);
    }

    /**
     * 计算考试排名
     */
    protected function calculateRankings(Exam $exam): void
    {
        $subjects = $exam->examSubjects->pluck('subject_id');

        foreach ($subjects as $subjectId) {
            // 获取该科目的所有有效成绩
            $scores = Score::where('exam_id', $exam->id)
                ->where('subject_id', $subjectId)
                ->where('is_absent', false)
                ->with('student.class')
                ->orderBy('score', 'desc')
                ->get();

            // 计算年级排名
            $gradeRank = 1;
            foreach ($scores as $score) {
                $score->update(['grade_rank' => $gradeRank]);
                $gradeRank++;
            }

            // 计算班级排名
            $classesByGrade = $scores->groupBy('student.class.grade_id');
            foreach ($classesByGrade as $gradeScores) {
                $classesByClass = $gradeScores->groupBy('student.class_id');
                foreach ($classesByClass as $classScores) {
                    $classRank = 1;
                    foreach ($classScores->sortByDesc('score') as $score) {
                        $score->update(['class_rank' => $classRank]);
                        $classRank++;
                    }
                }
            }
        }
    }

    /**
     * 生成邮箱地址
     */
    protected function generateEmail(string $name, string $type = 'teacher'): string
    {
        $pinyin = $this->convertToPinyin($name);
        $domains = [
            'teacher' => 'teacher.school.edu',
            'student' => 'student.school.edu',
            'parent' => 'parent.school.edu'
        ];
        $domain = $domains[$type] ?? 'school.edu';

        // 添加随机数避免重复
        $random = rand(100, 999);
        return $pinyin . $random . '@' . $domain;
    }

    /**
     * 生成用户名
     */
    protected function generateUsername(string $name): string
    {
        return $this->convertToPinyin($name) . rand(100, 999);
    }

    /**
     * 简单的中文转拼音 (简化版本)
     */
    protected function convertToPinyin(string $chinese): string
    {
        $pinyinMap = [
            '张' => 'zhang', '王' => 'wang', '李' => 'li', '刘' => 'liu', '陈' => 'chen',
            '杨' => 'yang', '赵' => 'zhao', '黄' => 'huang', '周' => 'zhou', '吴' => 'wu',
            '徐' => 'xu', '孙' => 'sun', '马' => 'ma', '朱' => 'zhu', '胡' => 'hu',
            '郭' => 'guo', '何' => 'he', '高' => 'gao', '林' => 'lin', '罗' => 'luo',
            '校' => 'xiao', '长' => 'zhang', '主' => 'zhu', '任' => 'ren', '老' => 'lao',
            '师' => 'shi', '语' => 'yu', '文' => 'wen', '数' => 'shu', '学' => 'xue',
            '英' => 'ying', '伟' => 'wei', '娜' => 'na', '丽' => 'li', '明' => 'ming',
            '钱' => 'qian', '冯' => 'feng', '卫' => 'wei', '蒋' => 'jiang', '韩' => 'han',
            '郑' => 'zheng', '物' => 'wu', '理' => 'li', '化' => 'hua', '生' => 'sheng',
            '政' => 'zheng', '治' => 'zhi', '历' => 'li', '史' => 'shi', '地' => 'di',
            '唐' => 'tang', '宋' => 'song', '薛' => 'xue', '范' => 'fan', '邓' => 'deng',
            '彭' => 'peng', '曾' => 'zeng', '萧' => 'xiao', '田' => 'tian', '董' => 'dong',
            '潘' => 'pan', '袁' => 'yuan', '于' => 'yu', '蔡' => 'cai', '余' => 'yu',
            '叶' => 'ye', '杜' => 'du', '程' => 'cheng', '魏' => 'wei', '梁' => 'liang',
            '谢' => 'xie', '朱' => 'zhu', '许' => 'xu', '何' => 'he', '高' => 'gao',
        ];

        $result = '';
        for ($i = 0; $i < mb_strlen($chinese); $i++) {
            $char = mb_substr($chinese, $i, 1);
            $result .= $pinyinMap[$char] ?? strtolower($char);
        }

        return strtolower($result);
    }
}
