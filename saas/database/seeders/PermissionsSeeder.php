<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取所有模块的权限
        $allPermissions = $this->getAllPermissions();
        
        // 插入权限记录
        foreach ($allPermissions as $permission) {
            DB::table('permissions')->insertOrIgnore([
                'name' => $permission['name'],
                'slug' => $permission['slug'],
                'module' => $permission['module'],
                'description' => $permission['description'] ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        // 创建默认角色
        $this->createDefaultRoles();
        
        // 为管理员角色分配所有权限
        $this->assignAllPermissionsToAdmin();
    }
    
    /**
     * 获取所有模块的权限
     */
    protected function getAllPermissions(): array
    {
        $permissions = [];
        
        // 获取每个模块的权限
        $modulePermissions = $this->getModulePermissions();
        
        // 合并所有模块的权限
        foreach ($modulePermissions as $module => $perms) {
            foreach ($perms as $perm) {
                $permissions[] = [
                    'name' => $perm['name'],
                    'slug' => $perm['slug'],
                    'module' => $module,
                    'description' => $perm['description'] ?? null,
                ];
            }
        }
        
        return $permissions;
    }
    
    /**
     * 获取每个模块的权限
     */
    protected function getModulePermissions(): array
    {
        return [
            // 核心模块权限
            'core' => [
                ['name' => '用户管理', 'slug' => 'manage-users', 'description' => '创建、编辑和删除用户'],
                ['name' => '角色管理', 'slug' => 'manage-roles', 'description' => '创建、编辑和删除角色'],
                ['name' => '权限管理', 'slug' => 'manage-permissions', 'description' => '分配权限给角色'],
                ['name' => '系统设置', 'slug' => 'manage-settings', 'description' => '修改系统设置'],
            ],
            
            // 成绩分析模块权限
            'score' => [
                ['name' => '考试管理', 'slug' => 'manage-exams', 'description' => '创建、编辑和删除考试'],
                ['name' => '成绩录入', 'slug' => 'enter-scores', 'description' => '录入和修改学生成绩'],
                ['name' => '成绩查询', 'slug' => 'view-scores', 'description' => '查看学生成绩'],
                ['name' => '成绩分析', 'slug' => 'analyze-scores', 'description' => '分析学生成绩数据'],
                ['name' => '成绩报表', 'slug' => 'generate-score-reports', 'description' => '生成成绩报表'],
            ],
            
            // 工资管理模块权限
            'salary' => [
                ['name' => '工资结构管理', 'slug' => 'manage-salary-structure', 'description' => '创建和修改工资结构'],
                ['name' => '工资计算', 'slug' => 'calculate-salaries', 'description' => '计算教师工资'],
                ['name' => '工资查询', 'slug' => 'view-salaries', 'description' => '查看工资记录'],
                ['name' => '工资报表', 'slug' => 'generate-salary-reports', 'description' => '生成工资报表'],
            ],
        ];
    }
    
    /**
     * 创建默认角色
     */
    protected function createDefaultRoles(): void
    {
        // 创建管理员角色
        DB::table('roles')->insertOrIgnore([
            'name' => '管理员',
            'description' => '系统管理员，拥有所有权限',
            'created_at' => now(),
        ]);
        
        // 创建教师角色
        DB::table('roles')->insertOrIgnore([
            'name' => '教师',
            'description' => '教师用户，可以查看成绩和工资',
            'created_at' => now(),
        ]);
        
        // 创建学生角色
        DB::table('roles')->insertOrIgnore([
            'name' => '学生',
            'description' => '学生用户，只能查看自己的成绩',
            'created_at' => now(),
        ]);
    }
    
    /**
     * 为管理员角色分配所有权限
     */
    protected function assignAllPermissionsToAdmin(): void
    {
        // 获取管理员角色ID
        $adminRole = DB::table('roles')->where('name', '管理员')->first();
        
        if (!$adminRole) {
            return;
        }
        
        // 获取所有权限ID
        $permissions = DB::table('permissions')->get();
        
        // 为管理员角色分配所有权限
        foreach ($permissions as $permission) {
            DB::table('role_permissions')->insertOrIgnore([
                'role_id' => $adminRole->id,
                'permission_id' => $permission->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
