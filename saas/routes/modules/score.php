<?php

declare(strict_types=1);

use App\Http\Controllers\ExamController;
use App\Http\Controllers\ExamThresholdController;
use App\Http\Controllers\ScoreController;
use App\Http\Controllers\ScoreImportController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\ClassController;
use App\Http\Controllers\GradeController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\ScoreAnalysisController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Score Module Routes
|--------------------------------------------------------------------------
|
| 成绩分析模块的路由定义
| 包含：考试管理、成绩录入、成绩分析、排名统计、报表导出等功能
|
*/

// 定义模块级别的Web路由
// 这些路由将在tenant中间件下访问并检查模块订阅权限
Route::middleware(['web', 'auth', 'check.data.scope:score'])->group(function () {
    // 成绩分析模块首页
    Route::get('/', function() {
        return inertia('tenant/score/Index');
    })->name('index');

    // 考试管理 - 需要管理员或教务主任权限
    Route::prefix('exams')->name('exams.')->group(function () {
        // 查看考试列表 - 所有角色都可以查看
        Route::get('/', function() {
            return inertia('tenant/exams/Index');
        })->name('index');

        // 创建和编辑考试 - 需要管理权限
        Route::middleware('check.user.role:principal,academic_director,grade_director')->group(function () {
            Route::get('/create', function() {
                return inertia('tenant/exams/Create');
            })->name('create');

            Route::get('/{id}/edit', function($id) {
                return inertia('tenant/exams/Edit', ['id' => $id]);
            })->name('edit');
        });

        // 查看考试详情 - 所有角色都可以查看
        Route::get('/{id}', function($id) {
            return inertia('tenant/exams/[id]', ['id' => $id]);
        })->name('show');
    });

    // 成绩管理
    Route::prefix('scores')->name('scores.')->group(function () {
        // 成绩查看 - 所有角色都可以查看（根据数据范围）
        Route::get('/exam/{id}', function($id) {
            return inertia('tenant/scores/exam/[id]', ['id' => $id]);
        })->name('exam');

        // 成绩录入 - 需要教师权限
        Route::middleware('check.user.role:principal,academic_director,grade_director,class_teacher,subject_teacher')->group(function () {
            Route::get('/exam/{id}/entry', function($id) {
                return inertia('tenant/scores/exam/Entry', ['id' => $id]);
            })->name('entry');
        });

        // 成绩分析 - 所有角色都可以查看（根据数据范围）
        Route::get('/exam/{id}/analysis', function($id) {
            return inertia('tenant/scores/exam/analysis/[id]', ['id' => $id]);
        })->name('analysis');

        Route::get('/exam/{id}/class-analysis', function($id) {
            return inertia('tenant/scores/exam/ClassAnalysis', ['id' => $id]);
        })->name('class_analysis');

        Route::get('/exam/{id}/subject-analysis', function($id) {
            return inertia('tenant/scores/exam/SubjectAnalysis', ['id' => $id]);
        })->name('subject_analysis');

        // 排名统计 - 所有角色都可以查看（根据数据范围）
        Route::get('/exam/{id}/rankings', function($id) {
            return inertia('tenant/scores/exam/Rankings', ['id' => $id]);
        })->name('rankings');

        Route::get('/exam/{id}/class/{classId}/rankings', function($id, $classId) {
            return inertia('tenant/scores/exam/ClassRankings', ['examId' => $id, 'classId' => $classId]);
        })->name('class_rankings');

        // 成绩导入 - 需要管理权限
        Route::middleware('check.user.role:principal,academic_director,grade_director,class_teacher')->group(function () {
            Route::get('/import', function() {
                return inertia('tenant/scores/import/Index');
            })->name('import');

            Route::get('/import/{id}', function($id) {
                return inertia('tenant/scores/import/[id]', ['id' => $id]);
            })->name('import.show');
        });

        // 成绩导出 - 需要教师以上权限
        Route::middleware('check.user.role:principal,academic_director,grade_director,class_teacher,subject_teacher')->group(function () {
            Route::get('/export', function() {
                return inertia('tenant/scores/Export');
            })->name('export');
        });
    });

    // 阈值管理
    Route::prefix('thresholds')->name('thresholds.')->group(function () {
        Route::get('/exam/{id}', function($id) {
            return inertia('tenant/thresholds/exam/[id]', ['id' => $id]);
        })->name('exam');

        Route::get('/settings', function() {
            return inertia('tenant/thresholds/Settings');
        })->name('settings');
    });

    // 学生管理
    Route::prefix('students')->name('students.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/students/Index');
        })->name('index');

        Route::get('/create', function() {
            return inertia('tenant/students/Create');
        })->name('create');

        Route::get('/{id}', function($id) {
            return inertia('tenant/students/[id]', ['id' => $id]);
        })->name('show');

        Route::get('/{id}/scores', function($id) {
            return inertia('tenant/students/Scores', ['id' => $id]);
        })->name('scores');

        Route::get('/{id}/analysis', function($id) {
            return inertia('tenant/students/Analysis', ['id' => $id]);
        })->name('analysis');
    });

    // 教师管理
    Route::prefix('teachers')->name('teachers.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/teachers/Index');
        })->name('index');

        Route::get('/create', function() {
            return inertia('tenant/teachers/Create');
        })->name('create');

        Route::get('/{id}', function($id) {
            return inertia('tenant/teachers/[id]', ['id' => $id]);
        })->name('show');

        Route::get('/{id}/classes', function($id) {
            return inertia('tenant/teachers/Classes', ['id' => $id]);
        })->name('classes');
    });

    // 班级管理
    Route::prefix('classes')->name('classes.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/classes/Index');
        })->name('index');

        Route::get('/create', function() {
            return inertia('tenant/classes/Create');
        })->name('create');

        Route::get('/{id}', function($id) {
            return inertia('tenant/classes/[id]', ['id' => $id]);
        })->name('show');

        Route::get('/{id}/students', function($id) {
            return inertia('tenant/classes/Students', ['id' => $id]);
        })->name('students');

        Route::get('/{id}/scores', function($id) {
            return inertia('tenant/classes/Scores', ['id' => $id]);
        })->name('scores');

        Route::get('/{id}/analysis', function($id) {
            return inertia('tenant/classes/Analysis', ['id' => $id]);
        })->name('analysis');
    });

    // 年级管理
    Route::prefix('grades')->name('grades.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/grades/Index');
        })->name('index');

        Route::get('/{id}', function($id) {
            return inertia('tenant/grades/[id]', ['id' => $id]);
        })->name('show');

        Route::get('/{id}/analysis', function($id) {
            return inertia('tenant/grades/Analysis', ['id' => $id]);
        })->name('analysis');
    });

    // 科目管理
    Route::prefix('subjects')->name('subjects.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/subjects/Index');
        })->name('index');

        Route::get('/{id}/analysis', function($id) {
            return inertia('tenant/subjects/Analysis', ['id' => $id]);
        })->name('analysis');
    });

    // 报表中心
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', function() {
            return inertia('tenant/reports/Index');
        })->name('index');

        Route::get('/exam/{id}', function($id) {
            return inertia('tenant/reports/ExamReport', ['id' => $id]);
        })->name('exam');

        Route::get('/class/{id}', function($id) {
            return inertia('tenant/reports/ClassReport', ['id' => $id]);
        })->name('class');

        Route::get('/student/{id}', function($id) {
            return inertia('tenant/reports/StudentReport', ['id' => $id]);
        })->name('student');
    });
});

// 定义模块的API路由
Route::name('api.')
    ->prefix('api')
    ->middleware(['api', 'auth:web', 'check.data.scope:score'])
    ->group(function () {
        // 考试相关API
        Route::apiResource('exams', ExamController::class);
        Route::prefix('exams')->group(function () {
            Route::get('/{id}/subjects', [ExamController::class, 'getSubjects']);

            // 需要管理权限的操作
            Route::middleware('check.user.role:principal,academic_director,grade_director')->group(function () {
                Route::post('/{id}/subjects', [ExamController::class, 'addSubjects']);
            });

            Route::get('/{id}/statistics', [ExamController::class, 'getStatistics']);
            Route::get('/{id}/participants', [ExamController::class, 'getParticipants']);
        });

        // 分数相关API
        Route::prefix('scores')->group(function () {
            Route::post('/batch', [ScoreController::class, 'batchStore']);
            Route::get('/export/{examId}', [ScoreController::class, 'export']);
            Route::get('/import-template/{examId}', [ScoreController::class, 'importTemplate']);
            Route::post('/import', [ScoreController::class, 'import']);
            Route::get('/analytics/{examId}', [ScoreController::class, 'getAnalytics']);
            Route::get('/analytics/total/{examId}', [ScoreController::class, 'getTotalScoreAnalytics']);
            Route::get('/rankings/class/{examId}/{classId}', [ScoreController::class, 'getClassRankings']);
            Route::get('/rankings/grade/{examId}/{gradeId}', [ScoreController::class, 'getGradeRankings']);
            Route::get('/rankings/total/class/{examId}/{classId}', [ScoreController::class, 'getTotalScoreClassRankings']);
            Route::get('/rankings/total/grade/{examId}/{gradeId}', [ScoreController::class, 'getTotalScoreGradeRankings']);
            Route::get('/student/{studentId}/history', [ScoreController::class, 'getStudentScoreHistory']);
            Route::get('/class/{classId}/comparison', [ScoreController::class, 'getClassComparison']);
            Route::get('/subject/{subjectId}/analysis', [ScoreController::class, 'getSubjectAnalysis']);
        });
        Route::apiResource('scores', ScoreController::class);

        // 学生相关API
        Route::apiResource('students', StudentController::class);
        Route::prefix('students')->group(function () {
            Route::get('/{id}/scores', [StudentController::class, 'getScores']);
            Route::get('/{id}/analysis', [StudentController::class, 'getAnalysis']);
            Route::get('/{id}/rankings', [StudentController::class, 'getRankings']);
            Route::post('/batch-import', [StudentController::class, 'batchImport']);
            Route::get('/export-template', [StudentController::class, 'exportTemplate']);
        });

        // 教师相关API
        Route::apiResource('teachers', TeacherController::class);
        Route::prefix('teachers')->group(function () {
            Route::get('/{id}/classes', [TeacherController::class, 'getClasses']);
            Route::get('/{id}/subjects', [TeacherController::class, 'getSubjects']);
            Route::get('/{id}/performance', [TeacherController::class, 'getPerformance']);
        });

        // 班级相关API
        Route::apiResource('classes', ClassController::class);
        Route::prefix('classes')->group(function () {
            Route::get('/{id}/students', [ClassController::class, 'getStudents']);
            Route::get('/{id}/scores/{examId}', [ClassController::class, 'getExamScores']);
            Route::get('/{id}/analysis/{examId}', [ClassController::class, 'getExamAnalysis']);
            Route::get('/{id}/rankings/{examId}', [ClassController::class, 'getExamRankings']);
            Route::get('/{id}/statistics', [ClassController::class, 'getStatistics']);
        });

        // 年级相关API
        Route::apiResource('grades', GradeController::class);
        Route::prefix('grades')->group(function () {
            Route::get('/{id}/classes', [GradeController::class, 'getClasses']);
            Route::get('/{id}/students', [GradeController::class, 'getStudents']);
            Route::get('/{id}/analysis/{examId}', [GradeController::class, 'getExamAnalysis']);
            Route::get('/{id}/rankings/{examId}', [GradeController::class, 'getExamRankings']);
        });

        // 科目相关API
        Route::apiResource('subjects', SubjectController::class);
        Route::prefix('subjects')->group(function () {
            Route::get('/{id}/analysis/{examId}', [SubjectController::class, 'getExamAnalysis']);
            Route::get('/{id}/statistics/{examId}', [SubjectController::class, 'getExamStatistics']);
            Route::get('/{id}/difficulty-analysis', [SubjectController::class, 'getDifficultyAnalysis']);
        });

        // 成绩分析API
        Route::prefix('analysis')->group(function () {
            Route::get('/exam/{examId}/overview', [ScoreAnalysisController::class, 'getExamOverview']);
            Route::get('/exam/{examId}/trends', [ScoreAnalysisController::class, 'getScoreTrends']);
            Route::get('/exam/{examId}/distribution', [ScoreAnalysisController::class, 'getScoreDistribution']);
            Route::get('/class/{classId}/progress', [ScoreAnalysisController::class, 'getClassProgress']);
            Route::get('/student/{studentId}/progress', [ScoreAnalysisController::class, 'getStudentProgress']);
            Route::get('/subject/{subjectId}/performance', [ScoreAnalysisController::class, 'getSubjectPerformance']);
        });

        // 成绩导入相关API
        Route::prefix('score-imports')->group(function () {
            Route::get('/', [ScoreImportController::class, 'index']);
            Route::post('/', [ScoreImportController::class, 'store']);
            Route::get('/{id}', [ScoreImportController::class, 'show']);
            Route::post('/{id}/upload', [ScoreImportController::class, 'upload']);
            Route::get('/{id}/preview', [ScoreImportController::class, 'preview']);
            Route::post('/{id}/detect-mapping', [ScoreImportController::class, 'detectMapping']);
            Route::post('/{id}/save-mapping', [ScoreImportController::class, 'saveMapping']);
            Route::post('/{id}/validate', [ScoreImportController::class, 'validateData']);
            Route::post('/{id}/execute', [ScoreImportController::class, 'execute']);
            Route::get('/{id}/progress', [ScoreImportController::class, 'progress']);
            Route::get('/{id}/result', [ScoreImportController::class, 'result']);
            Route::delete('/{id}', [ScoreImportController::class, 'destroy']);
        });

        // 导入模板相关API
        Route::prefix('import-templates')->group(function () {
            Route::get('/', [ScoreImportController::class, 'getTemplates']);
            Route::get('/{id}', [ScoreImportController::class, 'getTemplate']);
            Route::get('/{id}/download', [ScoreImportController::class, 'downloadTemplate']);
        });

        // 阈值相关API
        Route::prefix('thresholds')->group(function () {
            Route::get('/exam/{examId}', [ExamThresholdController::class, 'index']);
            Route::post('/batch', [ExamThresholdController::class, 'batchStore']);
            Route::get('/students-above/{examId}/{thresholdName}', [ExamThresholdController::class, 'getStudentsAboveThreshold']);
        });
        Route::apiResource('thresholds', ExamThresholdController::class)->except(['index']);

        // 报表相关API
        Route::prefix('reports')->group(function () {
            Route::get('/exam/{examId}', [ScoreController::class, 'generateExamReport']);
            Route::get('/class/{classId}', [ScoreController::class, 'generateClassReport']);
            Route::get('/student/{studentId}', [ScoreController::class, 'generateStudentReport']);
            Route::get('/grade/{gradeId}', [ScoreController::class, 'generateGradeReport']);
            Route::post('/custom', [ScoreController::class, 'generateCustomReport']);
        });
    });
