<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Tenant as Controllers;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Core Module Routes
|--------------------------------------------------------------------------
|
| 核心模块的路由定义，包含基础功能
|
*/

// 定义模块级别的Web路由
// 这些路由将在tenant中间件下访问
Route::middleware(['web', 'auth'])->group(function () {
    // 其他核心功能路由
    Route::get('/dashboard', function () {
        return Inertia::render('tenant/core/Dashboard');
    })->name('dashboard');

    Route::get('/profile', function () {
        return Inertia::render('tenant/core/Profile');
    })->name('profile');
});

// 定义模块的API路由
Route::name('api.')
    ->prefix('api')
    ->middleware(['api'])
    ->group(function () {
        // 核心模块API
        Route::get('/user', function () {
            return auth()->user();
        });
    });
