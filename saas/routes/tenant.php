<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Features\UserImpersonation;
use App\Http\Controllers\Tenant as Controllers;
use App\Http\Middleware\CheckSubscription;
use App\Http\Middleware\OwnerOnly;
use App\Http\Controllers\Tenant\BillingController;
use App\Http\Controllers\Tenant\ApplicationSettingsController;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Tenant\DomainController;
use App\Http\Controllers\Tenant\SubscriptionController;
use App\Http\Controllers\Tenant\BillingAddressController;
use App\Http\Controllers\Tenant\PaymentMethodController;

Route::middleware([
    'web',
    \Stancl\Tenancy\Middleware\InitializeTenancyByDomainOrSubdomain::class,
    \Stancl\Tenancy\Middleware\PreventAccessFromUnwantedDomains::class,
    \Stancl\Tenancy\Middleware\ScopeSessions::class,
])->group(function () {
    Route::name('tenant.')
        ->group(function () {
            require __DIR__.'/settings.php';
            require __DIR__.'/auth.php';

            Route::get('/impersonate/{token}', function ($token) {
                return UserImpersonation::makeResponse($token);
            })->name('impersonate');

            Route::post('/ploi/webhook/certificateIssued', [Controllers\PloiWebhookController::class, 'certificateIssued'])->name('ploi.certificate.issued');
            Route::post('/ploi/webhook/certificateRevoked', [Controllers\PloiWebhookController::class, 'certificateRevoked'])->name('ploi.certificate.revoked');

            // 租户首页 - 重定向到登录或仪表板
            Route::get('/', function () {
                if (Auth::check()) {
                    return redirect()->route('tenant.dashboard');
                }
                return redirect()->route('login');
            })->name('home');

            Route::middleware([
                'auth:web',
                'verified',
            ])->group(function () {
                Route::get('/dashboard', [App\Http\Controllers\Tenant\DashboardController::class, 'index'])
                    ->name('dashboard');

                // 订阅管理路由 - 所有用户都可以访问
                Route::prefix('subscription')->name('subscription.')->group(function () {
                    Route::get('/', [SubscriptionController::class, 'index'])->name('index');
                    Route::post('/checkout', [SubscriptionController::class, 'checkout'])->name('checkout');
                    Route::get('/success', [SubscriptionController::class, 'success'])->name('success');
                    Route::get('/cancel', [SubscriptionController::class, 'cancelPage'])->name('cancel');
                    Route::get('/required', [SubscriptionController::class, 'required'])->name('required');
                });

                Route::middleware(CheckSubscription::class)->group(function () {
                    // 加载所有模块路由
                    require __DIR__.'/modules.php';
                });

                Route::middleware(OwnerOnly::class)->group(function () {
                    Route::get('/settings/billing', [BillingController::class, 'show'])->name('settings.billing');
                    Route::get('/settings/billing/invoice/{id}/download', [BillingController::class, 'downloadInvoice'])->name('invoice.download');

                    Route::get('/settings/application', [ApplicationSettingsController::class, 'show'])->name('settings.application');
                    Route::get('/settings/domains', [ApplicationSettingsController::class, 'domains'])->name('settings.domains');
                    Route::post('/settings/application/configuration', [ApplicationSettingsController::class, 'storeConfiguration'])->name('settings.application.configuration');

                    Route::post('/domains/store', [DomainController::class, 'store'])->name('domains.store');
                    Route::post('/domains/store-fallback', [DomainController::class, 'storeFallback'])->name('domains.store-fallback');
                    Route::post('/domains/make-primary/{domain}', [DomainController::class, 'makePrimary'])->name('domains.make-primary');
                    Route::post('/domains/delete/{domain}', [DomainController::class, 'delete'])->name('domains.delete');

                    Route::post('/ploi/request-certificate/{domain}', [DomainController::class, 'requestCertificate'])->name('ploi.certificate.request');
                    Route::post('/ploi/revoke-certificate/{domain}', [DomainController::class, 'revokeCertificate'])->name('ploi.certificate.revoke');

                    Route::post('/billing-address/store', [BillingAddressController::class, 'store'])->name('billing-address.store');
                    Route::post('/subscription/update', [SubscriptionController::class, 'update'])->name('subscription.update');
                    Route::post('/subscription/resume', [SubscriptionController::class, 'resume'])->name('subscription.resume');
                    Route::post('/subscription/cancel/{reason}', [SubscriptionController::class, 'cancel'])->name('subscription.cancel');
                    Route::post('/payment-method/update', [PaymentMethodController::class, 'update'])->name('payment-method.update');
                });
            });
        });
});
