<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Module Routes
|--------------------------------------------------------------------------
|
| 这个文件负责加载所有模块的路由
| 每个模块都有自己的路由文件，放在routes/modules目录下
|
*/

// 核心模块路由 - 所有租户都可以访问
Route::prefix('core')->name('core.')->group(function () {
    require __DIR__.'/modules/core.php';
});

// 文章/公告模块路由 - 所有租户都可以访问
require __DIR__.'/modules/posts.php';

// 成绩分析模块路由 - 需要'score'模块访问权限
Route::middleware('ensure.module.access:score')
    ->prefix('scores')
    ->name('scores.')
    ->group(function () {
        require __DIR__.'/modules/score.php';
    });

// 工资管理模块路由 - 需要'salary'模块访问权限
Route::middleware('ensure.module.access:salary')
    ->prefix('salary')
    ->name('salary.')
    ->group(function () {
        require __DIR__.'/modules/salary.php';
    });

// 这里可以添加更多模块的路由
// 例如:
// Route::middleware('ensure.module.access:new_module')
//     ->prefix('new-module')
//     ->name('new_module.')
//     ->group(function () {
//         require __DIR__.'/modules/new_module.php';
//     });
