<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Central as Controllers;
use App\Http\Controllers\Admin;
use App\Http\Controllers\WebhookController;

// 主域名路由（宣传和注册）
Route::domain(config('app.central_domain'))->group(function () {
    // 主域名首页 - 系统宣传页面
    Route::get('/', [Controllers\WelcomeController::class, 'index'])->name('home');
    Route::get('/about', [Controllers\WelcomeController::class, 'about'])->name('about');
    Route::get('/contact', [Controllers\WelcomeController::class, 'contact'])->name('contact');
    Route::get('/pricing', [Controllers\WelcomeController::class, 'pricing'])->name('pricing');

    // Stripe Webhook 路由 - 不需要认证
    Route::post('/stripe/webhook', [WebhookController::class, 'handleWebhook'])->name('stripe.webhook');

    Route::name('central.')->group(function () {
        Route::get('/register', [Controllers\RegisterTenantController::class, 'show'])->name('register');
        Route::post('/register/submit', [Controllers\RegisterTenantController::class, 'submit'])->name('register.submit')
            ->middleware('throttle:create-tenant');

        Route::get('/login', [Controllers\LoginTenantController::class, 'show'])->name('login');
        Route::post('/login/submit', [Controllers\LoginTenantController::class, 'submit'])->name('login.submit');

        // 租户登录跳转
        Route::post('/tenant-login', [Controllers\WelcomeController::class, 'tenantLogin'])->name('tenant.login');
    });
});

// 管理后台路由
Route::domain(config('app.admin_domain'))->group(function () {
    Route::name('admin.')->group(function () {
        Route::redirect('/admin', '/admin/login');
        Route::get('/admin/login', [Admin\AuthController::class, 'show'])->name('login');
        Route::post('/admin/login/submit', [Admin\AuthController::class, 'login'])->name('login.submit');

        Route::middleware('auth:admin')->group(function () {
            Route::get('/admin/logout', [Admin\AuthController::class, 'logout'])->name('logout');

            Route::name('tenants.')->group(function () {
                Route::get('/admin/tenants', [Admin\TenantController::class, 'index'])->name('index');
                Route::get('/admin/tenants/create', [Admin\TenantController::class, 'create'])->name('create');
                Route::post('/admin/tenants/store', [Admin\TenantController::class, 'store'])->name('store');
                Route::get('/admin/tenants/{tenant}/edit', [Admin\TenantController::class, 'edit'])->name('edit');
                Route::get('/admin/tenants/{tenant}/impersonate', [Admin\TenantController::class, 'impersonate'])->name('impersonate');
                Route::put('/admin/tenants/{tenant}/update', [Admin\TenantController::class, 'update'])->name('update');
                Route::get('/admin/tenants/{tenant}/destroy', [Admin\TenantController::class, 'destroy'])->name('destroy');

                Route::name('billing.')->group(function () {
                    Route::get('/admin/tenants/{tenant}/billing', [Admin\BillingController::class, 'show'])->name('show');
                    Route::post('/admin/tenants/{tenant}/billing/adjustCredit', [Admin\BillingController::class, 'adjustCredit'])->name('adjustCredit');
                    Route::post('/admin/tenants/{tenant}/billing/updateAddress', [Admin\BillingController::class, 'updateAddress'])->name('updateAddress');
                });

                Route::name('domain.')->group(function () {
                    Route::get('/admin/tenants/{tenant}/domains', [Admin\DomainController::class, 'index'])->name('index');
                    Route::put('/domain/update/{domain}', [Admin\DomainController::class, 'update'])->name('update');
                    Route::post('/domain/store/{tenant}', [Admin\DomainController::class, 'store'])->name('store');
                    Route::get('/domain/destroy/{domain}', [Admin\DomainController::class, 'destroy'])->name('destroy');
                    Route::get('/domain/make-primary/{domain}', [Admin\DomainController::class, 'makePrimary'])->name('makePrimary');
                    Route::put('/domain/update-fallback/{tenant}', [Admin\DomainController::class, 'updateFallback'])->name('updateFallback');
                });
            });
        });
    });
});
