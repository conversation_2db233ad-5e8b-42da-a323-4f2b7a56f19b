<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">学生管理</h1>
        <p class="mt-2 text-gray-600">管理学生基本信息和学籍档案</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="showImportModal = true"
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <ArrowUpTrayIcon class="w-5 h-5 mr-2" />
          批量导入
        </button>
        <Link
          :href="route('score.students.create')"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          添加学生
        </Link>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索学生</label>
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              v-model="filters.search"
              type="text"
              placeholder="姓名、学号..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              @input="debouncedSearch"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">年级</label>
          <select
            v-model="filters.grade_id"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @change="applyFilters"
          >
            <option value="">全部年级</option>
            <option
              v-for="grade in grades"
              :key="grade.id"
              :value="grade.id"
            >
              {{ grade.name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">班级</label>
          <select
            v-model="filters.class_id"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @change="applyFilters"
          >
            <option value="">全部班级</option>
            <option
              v-for="classItem in filteredClasses"
              :key="classItem.id"
              :value="classItem.id"
            >
              {{ classItem.name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
          <select
            v-model="filters.gender"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @change="applyFilters"
          >
            <option value="">全部</option>
            <option value="male">男</option>
            <option value="female">女</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 学生列表 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">
            学生列表 ({{ students.total }} 人)
          </h2>
          <div class="flex items-center space-x-2">
            <button
              @click="exportStudents"
              class="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <ArrowDownTrayIcon class="w-4 h-4 inline mr-1" />
              导出
            </button>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学生信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                班级
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                联系方式
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                入学时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="student in students.data"
              :key="student.id"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <UserIcon class="w-6 h-6 text-gray-600" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ student.user.real_name }}
                    </div>
                    <div class="text-sm text-gray-500">
                      学号：{{ student.student_id }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ student.gender === 'male' ? '男' : '女' }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ student.class.name }}</div>
                <div class="text-sm text-gray-500">{{ student.class.grade.name }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ student.parent_name }}</div>
                <div class="text-sm text-gray-500">{{ student.parent_phone }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(student.admission_date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <Link
                    :href="route('score.students.show', student.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    查看
                  </Link>
                  <Link
                    :href="route('score.students.scores', student.id)"
                    class="text-green-600 hover:text-green-900"
                  >
                    成绩
                  </Link>
                  <Link
                    :href="route('score.students.analysis', student.id)"
                    class="text-purple-600 hover:text-purple-900"
                  >
                    分析
                  </Link>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="students.last_page > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ students.from }} 到 {{ students.to }} 条，共 {{ students.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              v-for="page in paginationPages"
              :key="page"
              @click="goToPage(page)"
              :class="[
                'px-3 py-2 text-sm rounded-lg transition-colors',
                page === students.current_page
                  ? 'bg-blue-600 text-white'
                  : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import {
  PlusIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  UserIcon,
} from '@heroicons/vue/24/outline'

// 接收从后端传递的数据
const props = defineProps({
  students: Object,
  grades: Array,
  classes: Array,
  filters: Object
})

// 响应式数据
const filters = ref({
  search: props.filters.search || '',
  grade_id: props.filters.grade_id || '',
  class_id: props.filters.class_id || '',
  gender: props.filters.gender || ''
})

const showImportModal = ref(false)

// 计算属性
const filteredClasses = computed(() => {
  if (!filters.value.grade_id) {
    return props.classes
  }
  return props.classes.filter(c => c.grade_id == filters.value.grade_id)
})

const paginationPages = computed(() => {
  const pages = []
  const current = props.students.current_page
  const last = props.students.last_page
  
  for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  router.get(route('score.students.index'), filters.value, {
    preserveState: true,
    preserveScroll: true
  })
}

const goToPage = (page) => {
  router.get(route('score.students.index'), {
    ...filters.value,
    page
  }, {
    preserveState: true,
    preserveScroll: true
  })
}

const exportStudents = () => {
  window.open(route('score.api.students.export-template') + '?' + new URLSearchParams(filters.value))
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 监听年级变化，重置班级选择
watch(() => filters.value.grade_id, () => {
  filters.value.class_id = ''
})
</script>
