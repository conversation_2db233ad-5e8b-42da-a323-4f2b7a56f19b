<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">成绩分析系统</h1>
        <p class="mt-2 text-gray-600">全面的学生成绩管理与分析平台</p>
      </div>
      <div class="flex space-x-3">
        <Link
          :href="route('score.exams.create')"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          创建考试
        </Link>
        <Link
          :href="route('score.scores.import')"
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <ArrowUpTrayIcon class="w-5 h-5 mr-2" />
          导入成绩
        </Link>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100">
            <AcademicCapIcon class="w-6 h-6 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总学生数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalStudents }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100">
            <DocumentTextIcon class="w-6 h-6 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">考试场次</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalExams }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100">
            <ChartBarIcon class="w-6 h-6 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">成绩记录</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalScores }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100">
            <UserGroupIcon class="w-6 h-6 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">班级数量</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalClasses }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">快速导航</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 考试管理 -->
          <Link
            :href="route('score.exams.index')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
                <DocumentTextIcon class="w-6 h-6 text-blue-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">考试管理</h3>
                <p class="text-sm text-gray-500">创建和管理考试</p>
              </div>
            </div>
          </Link>

          <!-- 成绩录入 -->
          <Link
            :href="route('score.scores.import')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-green-50 rounded-lg group-hover:bg-green-100 transition-colors">
                <ArrowUpTrayIcon class="w-6 h-6 text-green-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">成绩录入</h3>
                <p class="text-sm text-gray-500">批量导入成绩数据</p>
              </div>
            </div>
          </Link>

          <!-- 学生管理 -->
          <Link
            :href="route('score.students.index')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-purple-50 rounded-lg group-hover:bg-purple-100 transition-colors">
                <AcademicCapIcon class="w-6 h-6 text-purple-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">学生管理</h3>
                <p class="text-sm text-gray-500">管理学生信息</p>
              </div>
            </div>
          </Link>

          <!-- 班级管理 -->
          <Link
            :href="route('score.classes.index')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-yellow-50 rounded-lg group-hover:bg-yellow-100 transition-colors">
                <UserGroupIcon class="w-6 h-6 text-yellow-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">班级管理</h3>
                <p class="text-sm text-gray-500">管理班级信息</p>
              </div>
            </div>
          </Link>

          <!-- 教师管理 -->
          <Link
            :href="route('score.teachers.index')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-indigo-50 rounded-lg group-hover:bg-indigo-100 transition-colors">
                <UserIcon class="w-6 h-6 text-indigo-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">教师管理</h3>
                <p class="text-sm text-gray-500">管理教师信息</p>
              </div>
            </div>
          </Link>

          <!-- 报表中心 -->
          <Link
            :href="route('score.reports.index')"
            class="group p-4 border border-gray-200 rounded-lg hover:border-red-300 hover:shadow-md transition-all"
          >
            <div class="flex items-center">
              <div class="p-2 bg-red-50 rounded-lg group-hover:bg-red-100 transition-colors">
                <ChartBarIcon class="w-6 h-6 text-red-600" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900">报表中心</h3>
                <p class="text-sm text-gray-500">生成各类分析报表</p>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>

    <!-- 最近考试 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">最近考试</h2>
          <Link
            :href="route('score.exams.index')"
            class="text-sm text-blue-600 hover:text-blue-700"
          >
            查看全部
          </Link>
        </div>
      </div>
      <div class="p-6">
        <div v-if="recentExams.length === 0" class="text-center py-8">
          <DocumentTextIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">暂无考试数据</p>
          <Link
            :href="route('score.exams.create')"
            class="mt-2 inline-flex items-center text-blue-600 hover:text-blue-700"
          >
            创建第一个考试
          </Link>
        </div>
        <div v-else class="space-y-4">
          <div
            v-for="exam in recentExams"
            :key="exam.id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
          >
            <div>
              <h3 class="font-medium text-gray-900">{{ exam.name }}</h3>
              <p class="text-sm text-gray-500">{{ exam.exam_type }} • {{ exam.start_date }}</p>
            </div>
            <div class="flex space-x-2">
              <Link
                :href="route('score.scores.exam', exam.id)"
                class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              >
                查看成绩
              </Link>
              <Link
                :href="route('score.scores.analysis', exam.id)"
                class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
              >
                成绩分析
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import {
  PlusIcon,
  ArrowUpTrayIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  ChartBarIcon,
  UserGroupIcon,
  UserIcon,
} from '@heroicons/vue/24/outline'

// 接收从后端传递的数据
defineProps({
  stats: {
    type: Object,
    default: () => ({
      totalStudents: 0,
      totalExams: 0,
      totalScores: 0,
      totalClasses: 0,
    })
  },
  recentExams: {
    type: Array,
    default: () => []
  }
})
</script>
