<template>
  <div class="max-w-4xl mx-auto">
    <!-- 页面标题 -->
    <div class="mb-8">
      <div class="flex items-center space-x-4">
        <Link
          :href="route('score.exams.index')"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ArrowLeftIcon class="w-6 h-6" />
        </Link>
        <div>
          <h1 class="text-3xl font-bold text-gray-900">创建考试</h1>
          <p class="mt-2 text-gray-600">设置考试基本信息和科目安排</p>
        </div>
      </div>
    </div>

    <!-- 表单 -->
    <form @submit.prevent="submit" class="space-y-8">
      <!-- 基本信息 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">基本信息</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              考试名称 <span class="text-red-500">*</span>
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="例如：高一年级期中考试"
            />
            <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">
              {{ form.errors.name }}
            </div>
          </div>

          <div>
            <label for="exam_type" class="block text-sm font-medium text-gray-700 mb-2">
              考试类型 <span class="text-red-500">*</span>
            </label>
            <select
              id="exam_type"
              v-model="form.exam_type"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">请选择考试类型</option>
              <option value="monthly">月考</option>
              <option value="midterm">期中考试</option>
              <option value="final">期末考试</option>
              <option value="mock">模拟考试</option>
              <option value="entrance">入学考试</option>
            </select>
            <div v-if="form.errors.exam_type" class="mt-1 text-sm text-red-600">
              {{ form.errors.exam_type }}
            </div>
          </div>

          <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
              开始日期 <span class="text-red-500">*</span>
            </label>
            <input
              id="start_date"
              v-model="form.start_date"
              type="date"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div v-if="form.errors.start_date" class="mt-1 text-sm text-red-600">
              {{ form.errors.start_date }}
            </div>
          </div>

          <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
              结束日期 <span class="text-red-500">*</span>
            </label>
            <input
              id="end_date"
              v-model="form.end_date"
              type="date"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div v-if="form.errors.end_date" class="mt-1 text-sm text-red-600">
              {{ form.errors.end_date }}
            </div>
          </div>

          <div>
            <label for="academic_year" class="block text-sm font-medium text-gray-700 mb-2">
              学年 <span class="text-red-500">*</span>
            </label>
            <input
              id="academic_year"
              v-model="form.academic_year"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="例如：2024-2025"
            />
            <div v-if="form.errors.academic_year" class="mt-1 text-sm text-red-600">
              {{ form.errors.academic_year }}
            </div>
          </div>

          <div>
            <label for="semester" class="block text-sm font-medium text-gray-700 mb-2">
              学期 <span class="text-red-500">*</span>
            </label>
            <select
              id="semester"
              v-model="form.semester"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">请选择学期</option>
              <option value="spring">春季学期</option>
              <option value="autumn">秋季学期</option>
            </select>
            <div v-if="form.errors.semester" class="mt-1 text-sm text-red-600">
              {{ form.errors.semester }}
            </div>
          </div>
        </div>

        <div class="mt-6">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
            考试说明
          </label>
          <textarea
            id="description"
            v-model="form.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="考试的详细说明和注意事项..."
          ></textarea>
          <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">
            {{ form.errors.description }}
          </div>
        </div>
      </div>

      <!-- 科目设置 -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900">科目设置</h2>
          <button
            type="button"
            @click="addSubject"
            class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon class="w-4 h-4 mr-1" />
            添加科目
          </button>
        </div>

        <div v-if="form.subjects.length === 0" class="text-center py-8 text-gray-500">
          <BookOpenIcon class="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p>暂未添加考试科目</p>
          <button
            type="button"
            @click="addSubject"
            class="mt-2 text-blue-600 hover:text-blue-700"
          >
            点击添加第一个科目
          </button>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="(subject, index) in form.subjects"
            :key="index"
            class="p-4 border border-gray-200 rounded-lg"
          >
            <div class="flex items-center justify-between mb-4">
              <h3 class="font-medium text-gray-900">科目 {{ index + 1 }}</h3>
              <button
                type="button"
                @click="removeSubject(index)"
                class="text-red-600 hover:text-red-700"
              >
                <TrashIcon class="w-5 h-5" />
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  科目名称 <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="subject.subject_id"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择科目</option>
                  <option
                    v-for="availableSubject in availableSubjects"
                    :key="availableSubject.id"
                    :value="availableSubject.id"
                  >
                    {{ availableSubject.name }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  满分 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="subject.full_score"
                  type="number"
                  min="0"
                  step="0.1"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  及格分 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="subject.pass_score"
                  type="number"
                  min="0"
                  step="0.1"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  考试日期
                </label>
                <input
                  v-model="subject.exam_date"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  开始时间
                </label>
                <input
                  v-model="subject.start_time"
                  type="time"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  结束时间
                </label>
                <input
                  v-model="subject.end_time"
                  type="time"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="flex items-center justify-end space-x-4">
        <Link
          :href="route('score.exams.index')"
          class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          取消
        </Link>
        <button
          type="submit"
          :disabled="form.processing"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="form.processing">创建中...</span>
          <span v-else>创建考试</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Link, useForm } from '@inertiajs/vue3'
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  BookOpenIcon,
} from '@heroicons/vue/24/outline'

// 接收从后端传递的数据
const props = defineProps({
  availableSubjects: {
    type: Array,
    default: () => []
  }
})

// 表单数据
const form = useForm({
  name: '',
  exam_type: '',
  start_date: '',
  end_date: '',
  academic_year: '2024-2025',
  semester: '',
  description: '',
  subjects: []
})

// 添加科目
const addSubject = () => {
  form.subjects.push({
    subject_id: '',
    full_score: 100,
    pass_score: 60,
    exam_date: form.start_date,
    start_time: '09:00',
    end_time: '11:00',
    weight: 1.0
  })
}

// 移除科目
const removeSubject = (index) => {
  form.subjects.splice(index, 1)
}

// 提交表单
const submit = () => {
  form.post(route('score.api.exams.store'), {
    onSuccess: () => {
      // 成功后跳转到考试列表
    }
  })
}
</script>
