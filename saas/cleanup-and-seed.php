<?php

/**
 * 清理并重新初始化数据库的脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "开始清理数据库...\n";

try {
    // 1. 删除所有租户数据库
    echo "正在删除租户数据库...\n";
    
    // 获取所有租户
    $tenants = DB::table('tenants')->get();
    
    foreach ($tenants as $tenant) {
        $databaseName = 'tenant' . $tenant->id;
        try {
            DB::statement("DROP DATABASE IF EXISTS {$databaseName}");
            echo "已删除数据库: {$databaseName}\n";
        } catch (Exception $e) {
            echo "删除数据库 {$databaseName} 时出错: " . $e->getMessage() . "\n";
        }
    }
    
    // 2. 清理中央数据库
    echo "正在清理中央数据库表...\n";
    
    $tables = [
        'tenant_user_impersonation_tokens',
        'domains',
        'tenants',
        'subscription_cancelations',
        'subscription_items', 
        'subscriptions',
        'jobs',
        'cache',
        'admins'
    ];
    
    DB::statement('SET FOREIGN_KEY_CHECKS=0');
    foreach ($tables as $table) {
        try {
            DB::statement("DROP TABLE IF EXISTS {$table}");
            echo "已删除表: {$table}\n";
        } catch (Exception $e) {
            echo "删除表 {$table} 时出错: " . $e->getMessage() . "\n";
        }
    }
    DB::statement('SET FOREIGN_KEY_CHECKS=1');
    
    echo "数据库清理完成！\n";
    
    // 3. 重新运行迁移
    echo "正在运行迁移...\n";
    Artisan::call('migrate', ['--force' => true]);
    echo Artisan::output();
    
    // 4. 运行种子数据
    echo "正在运行种子数据...\n";
    Artisan::call('db:seed', ['--force' => true]);
    echo Artisan::output();
    
    echo "数据库初始化完成！\n";
    
} catch (Exception $e) {
    echo "发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
